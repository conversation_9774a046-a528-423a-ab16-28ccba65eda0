#!/usr/bin/env bash
set -x
source ../repo.cfg
# shellcheck disable=SC2155
export RAND=$(date +%s)
COMPONENT=$1

create_network () {
  echo "Creating network"
  docker network create tests-"${PROJECT}-${COMPONENT}-${RAND}"
}
cleanup () {
  echo "Removing the network"
  docker network rm tests-"${PROJECT}-${COMPONENT}-${RAND}" || echo "Error removing network"
}

exit_on_failure () {
   if [ $? -ne 0 ]; then
     cleanup
     exit 3
   fi
}

run_tests () {
  docker build -t tests-"${PROJECT}-${COMPONENT}-${RAND}" -f Dockerfile.ci .
  docker run --rm \
    --name tests-"${PROJECT}-${COMPONENT}-${RAND}" \
    --net=tests-"${PROJECT}-${COMPONENT}-${RAND}" \
    -v "$(pwd):/usr/src/application/" \
    tests-"${PROJECT}-${COMPONENT}-${RAND}"
  exit_on_failure
}

create_network
run_tests
cleanup
