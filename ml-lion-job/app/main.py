#!/usr/bin/env python3
"""
Main script to run the automatic labeling pipeline.

This script processes a set of images and LiDAR data from a given sequence
and produces label mappings between 2D camera images and 3D LiDAR data.
"""

import os
import sys
from pathlib import Path
import json
from glob import glob

from src.data_utils import requires_data
from src.data_utils import sync_to_s3
from src.data_utils import S3_BUCKET_NAME
from src.utils.file_mapping import map_files_for_json
from src.utils.format_conversion import create_bolf_json
from src.core.detection import get_2d_detections, get_3d_detections
from src.core.detection.inference import run_3d_2d_inference
from src.core.detection.track_2d import run_tracking_all_cams
from src.core.calibration import get_calibration
from src.core.projection import CuboidProjector
from src.core.mapper import map_3d_to_2d_detections, create_output_json
from src.visualization import visualize_detections
from src.core.detection.filter_3d_tracks import filter_and_reassign_3d_tracks
from src.core.detection.improved_pipeline import run_improved_tracking_pipeline

from utils import config
from utils import logger
from utils import logger_params

logger.info(f"*******{config.pipeline_settings.seq_dir}", **logger_params)

@requires_data(files=[config.pipeline_settings.seq_dir, config.pipeline_settings.model_path_3d],)
def main():
    """Main function to run the pipeline."""
    logger.info("Starting ML-Lion pipeline...", **logger_params)
    pipeline_settings = config.get('pipeline_settings', {})

    # Extract settings from config
    seq_dir = pipeline_settings.get('seq_dir', 'test_seq')
    output_dir = pipeline_settings.get('output_dir', 'outputs')
    visualize = pipeline_settings.get('visualize', False)
    run_inference = pipeline_settings.get('run_inference', True)
    camera_sensors = pipeline_settings.get('camera_sensors',
                                         ['FC1', 'TV_right', 'TV_left', 'TV_front', 'TV_rear', 'RefLidar'])
    single_file = pipeline_settings.get('single_file')

    # Create output directories if they don't exist
    os.makedirs(output_dir, exist_ok=True)
    combined_output_dir = os.path.join(output_dir, 'combined_output')
    os.makedirs(combined_output_dir, exist_ok=True)
    if visualize:
        visualization_dir = os.path.join(output_dir, 'visualizations')
        os.makedirs(visualization_dir, exist_ok=True)

    detection_3d_output_folder = os.path.join(output_dir, '3d_detection_output')
    tracking_3d_output_folder = os.path.join(output_dir, '3d_tracking_output')
    detection_2d_output_folder = os.path.join(output_dir, '2d_detection_output')

    # Get list of JSON files to process
    if single_file:
        json_files = [single_file]
    else:
        json_files = sorted([f for f in glob(os.path.join(seq_dir, "*.json"))
                           if "LIDAR" in f or "_FC1" not in f])

    all_mappings = []
    logger.info(f"Found {len(json_files)} JSON files to process", **logger_params)
    for json_i, json_file_path in enumerate(json_files):
        logger.info(f"\nProcessing: {json_file_path}", **logger_params)

        # Map files for this JSON
        file_mapping = map_files_for_json(json_file_path, seq_dir)
        # quit()
        if not file_mapping:
            logger.info(f"No files mapped for {json_file_path}. Skipping.", **logger_params)
            continue
        all_mappings.append(file_mapping)

    mapping_file_path = os.path.join(output_dir, 'file_mappings.json')
    try:
        # Create a serializable version of the mappings
        serializable_mappings = []

        for mapping in all_mappings:
            # Create a new dict with only serializable values
            clean_mapping = {}

            # Convert the mapping to be JSON serializable
            for key, value in mapping.items():
                # Keep string values and numbers
                if isinstance(value, (str, int, float)):
                    clean_mapping[key] = value
                # Skip non-serializable values
                else:
                    clean_mapping[key] = str(value)

            # Add the frame number if available
            if 'FC1' in mapping:
                fc1_basename = os.path.basename(mapping['FC1'])
                clean_mapping['fc1_basename'] = fc1_basename

            serializable_mappings.append(clean_mapping)

        with open(mapping_file_path, 'w') as f:
            json.dump(serializable_mappings, f, indent=2)

        logger.info(f"\nSaved file mappings for {len(serializable_mappings)} frames to {mapping_file_path}", **logger_params)
    except Exception as e:
        logger.info(f"Error saving file mappings: {e}", **logger_params)
        

    if run_inference:
        os.makedirs(detection_3d_output_folder, exist_ok=True)
        os.makedirs(tracking_3d_output_folder, exist_ok=True)
        os.makedirs(detection_2d_output_folder, exist_ok=True)
        point_cloud_extension = pipeline_settings.get('point_cloud_extension')
        model_path_3d = pipeline_settings.get('model_path_3d')
        model_confg_path_3d = pipeline_settings.get('model_cfg_path_3d')

        # Check if we should use the improved pipeline
        use_improved_pipeline = pipeline_settings.get('use_improved_pipeline', False)
        
        if use_improved_pipeline:
            logger.info("Using improved unified tracking pipeline...", **logger_params)
            
            # Create unified config
            unified_config = {
                'camera_sensors': camera_sensors,
                '3d_tracking_config': config.get('3d_tracking_config', {}),
                '2d_tracking_config': config.get('2d_tracking_config', {}),
                'association_config': config.get('unified_tracking_config', {}).get('association_config', {}),
                'max_track_age': config.get('unified_tracking_config', {}).get('max_track_age', 10),
                'use_improved_pipeline': True
            }
            
            # Run the improved pipeline
            run_3d_2d_inference(seq_dir,
                                point_cloud_extension,
                                model_path_3d,
                                model_confg_path_3d,
                                detection_3d_output_folder,
                                tracking_3d_output_folder,
                                detection_2d_output_folder,
                                unified_config)
        else:
            # Original pipeline
            run_3d_2d_inference(seq_dir,
                                point_cloud_extension,
                                model_path_3d,
                                model_confg_path_3d,
                                detection_3d_output_folder,
                                tracking_3d_output_folder,
                                detection_2d_output_folder,
                                config)
    config['3d_detection'] = tracking_3d_output_folder
    config['2d_detection'] = os.path.join(detection_2d_output_folder, "detections_2d.json")
    print("Filtering and reassigning 3D track IDs...")
    tracking_3d_input_folder = os.path.join(output_dir, '3d_tracking_output')
    tracking_3d_filtered_folder = os.path.join(output_dir, '3d_tracking_filtered')
    
    if os.path.exists(tracking_3d_input_folder):
        id_mapping = filter_and_reassign_3d_tracks(
            tracking_3d_input_folder, 
            tracking_3d_filtered_folder, 
            min_frames=3
        )
        # Update config to point to filtered results
        config['3d_detection'] = tracking_3d_filtered_folder
        print(f"Filtered 3D tracking results saved to: {tracking_3d_filtered_folder}")
    else:
        print(f"Warning: 3D tracking folder not found: {tracking_3d_input_folder}")
    

    # Get max track id for 3d tracks
    lidar_files_3d = os.listdir(tracking_3d_output_folder)
    max_track_id = 0
    for lidar_file in lidar_files_3d:
        if lidar_file.endswith(".json"):
            with open(os.path.join(tracking_3d_output_folder, lidar_file), 'r') as f:
                data = json.load(f)
                for obj in data:
                    if 'identity' in obj:
                        if obj['identity'] > max_track_id:
                            max_track_id = obj['identity']

    logger.info(f"Found max track id: {max_track_id}", **logger_params)
    logger.info(f"Assigning next id from: {max_track_id + 100}", **logger_params)
    next_available_id = max_track_id + 100

    all_mappings = []
    logger.info(f"Found {len(json_files)} JSON files to process", **logger_params)
    for json_i, json_file_path in enumerate(json_files):
        logger.info(f"\nProcessing: {json_file_path}", **logger_params)

        # Map files for this JSON
        file_mapping = map_files_for_json(json_file_path, seq_dir)
        # quit()
        if not file_mapping:
            logger.info(f"No files mapped for {json_file_path}. Skipping.", **logger_params)
            continue
        all_mappings.append(file_mapping)
        logger.info(f"Mapped {len(file_mapping)} files:", **logger_params)
        for stream, file_path in file_mapping.items():
            logger.info(f"  {stream}: {os.path.basename(file_path)}", **logger_params)

        # Get calibration data
        calibration_data = get_calibration(
            json_file_path,
            'RefLidar',
            camera_sensors
        )

        if not calibration_data or not calibration_data.get('calibration'):
            logger.info("Failed to get calibration data. Skipping.", **logger_params)
            continue

        # Initialize cuboid projector with the calibration data
        projector = CuboidProjector(calibration_data)

        # Get 3D detections
        lidar_file = file_mapping.get('RefLidar')
        if not lidar_file:
            logger.info("No LiDAR file found in mapping. Skipping.", **logger_params)
            continue

        detections_3d = get_3d_detections(lidar_file, config['3d_detection'])
        if not detections_3d:
            logger.info("No 3D detections found. Skipping.", **logger_params)
            continue

        logger.info(f"Found {len(detections_3d)} 3D detections", **logger_params)

        # Get 2D detections
        detections_2d = get_2d_detections(file_mapping, config['2d_detection'])
        if not detections_2d:
            logger.info("No 2D detections found. Skipping.", **logger_params)
            continue

        logger.info(f"Found 2D detections for {len(detections_2d)} cameras", **logger_params)

        # Map 3D to 2D detections
        mapping, next_available_id = map_3d_to_2d_detections(file_mapping, detections_3d, detections_2d, projector, next_available_id)

        # Print mapping summary
        match_count = sum(1 for obj in mapping.values() if obj['matches'])
        logger.info(f"Found matches for {match_count} out of {len(mapping)} 3D detections", **logger_params)

        # Create output JSON file
        logger.info(f"{file_mapping.keys()}, {file_mapping['FC1']}", **logger_params)
        basename = os.path.basename(file_mapping['FC1'])
        logger.info(f"Creating output JSON for {basename}", **logger_params)
        output_json_path = os.path.join(combined_output_dir, f'{basename}'.replace('.png','.json' ))
        create_output_json(mapping, file_mapping, output_json_path)

        # Generate visualizations if requested
        if visualize:
            visualize_detections(
                output_json_path,
                seq_dir,
                visualization_dir
            )
            logger.info(f"Visualizations saved to {visualization_dir}", **logger_params)


    # Track 2d detections
    # Assign consistent IDs to 2D detections which don't have corresponding 3D detections
    logger.info("Tracking in 2d ...", **logger_params)
    tracking_2d_output_folder = os.path.join(output_dir, '2d_tracking_json_output')
    os.makedirs(tracking_2d_output_folder, exist_ok=True)
    run_tracking_all_cams(seq_dir, combined_output_dir, tracking_2d_output_folder, camera_sensors[:-1], config.get('2d_tracking_config'))


    logger.info("Converting to BOLF Format ...", **logger_params)
    bolf_filepath = os.path.join(output_dir, 'bolf.json')
    logger.info(f"Creating BOLF JSON at bolf_filepath={bolf_filepath}", **logger_params)
    create_bolf_json(tracking_2d_output_folder, seq_dir, bolf_filepath)
    logger.info("\nPipeline completed successfully!", **logger_params)
    logger.info(f"Syncing output directory {output_dir} to S3 bucket {S3_BUCKET_NAME}", **logger_params)
    sync_to_s3(output_dir, S3_BUCKET_NAME, output_dir[5:]) # Remove 'data/' prefix from output_dir


if __name__ == "__main__":
    main()
