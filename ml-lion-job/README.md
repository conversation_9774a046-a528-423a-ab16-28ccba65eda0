# ml-lion-job

Add component description here

# Getting Started

## Running in local

### Setting up
```
# Clone Repo
cd /path/to/project
export PYTHONPATH=$PWD
export HOSTNAME=$HOSTNAME #optional
export LOG_LEVEL=10       #optional
```

### Running application
```
cd /path/to/projectsrepo/ml-lion/ml-lion-job
python3 app/main.py
```



## ECR Repo

| **Environment**      | **branch_name** | **ECR Link**                                                                                                  |
|----------------------|-----------------|---------------------------------------------------------------------------------------------------------------|
| Production          | main            | 373298745610.dkr.ecr.us-east-1.amazonaws.com/ml-lion/ml-lion-job:stable     |
| Staging  | staging         | 373298745610.dkr.ecr.us-east-1.amazonaws.com/ml-lion/ml-lion-job:latest     |
| Development| dev             | 373298745610.dkr.ecr.us-east-1.amazonaws.com/ml-lion/ml-lion-job:dev        |
| Playground      | playground      | 373298745610.dkr.ecr.us-east-1.amazonaws.com/ml-lion/ml-lion-job:playground |
