project_name: ml-lion
app_name: ml-lion-job
log:
  backup_count: 6
  file: data/logs/ml-lion_ml-lion-job.log
  format: 'ts=%(asctime)s host=%(host)s rid=%(rid)s lvl=%(levelname)-8s %(message)s'
  interval: 10
  level: ${oc.decode:${oc.env:LOG_LEVEL,10}}
  rotation_mode: M
  save_log_file: ${oc.decode:${oc.env:SAVE_LOG_FILE, False}}
user: user
host: ${oc.env:HOSTNAME, unknown}
pipeline_settings:
  seq_dir: data/${oc.env:S3_PREFIX}/
  output_dir: data/${oc.env:S3_PREFIX}/outputs${oc.env:OUTPUT_SUFFIX,""}
  visualize: false
  run_inference: true
  use_improved_pipeline: true
  point_cloud_extension: .pcd
  model_path_3d: data/models/ml-pc-object-detect/lion/v1/checkpoint_epoch_12_argov2_mamba.pth
  model_cfg_path_3d: tools/cfgs/lion_models/lion_mamba_1f_1x_argo_128dim_sparse_v2.yaml
  camera_sensors: ["FC1", "TV_right", "TV_left", "TV_front", "TV_rear", "RefLidar"]
  single_file: null

3d_detection_config:
  device: cuda
  default_intensity_pcd: 127
  log_intensity_range: true

2d_detection_config:
  model_type: ultralytics
  model_path: yolo11x.pt
  device: cuda
  base_confidence_threshold: 0.1
  default_slice_height: 640
  default_slice_width: 640
  overlap_height_ratio: 0.3
  overlap_width_ratio: 0.3
  filter_class_ids: [0, 1, 2, 3, 5, 7]
  show_results: true
  print_info: false
  
  camera_specific_settings:
    FC1:
      confidence_threshold: 0.4
      slice_height: 512
      slice_width: 512
      postprocess_match_threshold: 0.5
      intersection_threshold: 0.8
      min_side_threshold: 30
      max_side_threshold: 1500
      filter_boundary_boxes: false
    
    TVfront:
      confidence_threshold: 0.4
      slice_height: 356
      slice_width: 356
      postprocess_match_threshold: 0.5
      intersection_threshold: 0.8
      min_side_threshold: 0
      max_side_threshold: 1000
      filter_boundary_boxes: false
    
    TVrear:
      confidence_threshold: 0.4
      slice_height: 356
      slice_width: 356
      postprocess_match_threshold: 0.5
      intersection_threshold: 0.8
      min_side_threshold: 0
      max_side_threshold: 1000
      filter_boundary_boxes: false
    
    TVleft:
      confidence_threshold: 0.4
      slice_height: 356
      slice_width: 356
      postprocess_match_threshold: 1.0
      intersection_threshold: 0.8
      min_side_threshold: 0
      max_side_threshold: 1000
      filter_boundary_boxes: true
      boundary_x_max: 1486
      boundary_x_min: 50
      boundary_y_min_height: 120
    
    TVright:
      confidence_threshold: 0.4
      slice_height: 356
      slice_width: 356
      postprocess_match_threshold: 1.0
      intersection_threshold: 0.8
      min_side_threshold: 0
      max_side_threshold: 1000
      filter_boundary_boxes: true
      boundary_x_max: 1486
      boundary_x_min: 50
      boundary_y_min_height: 120

  greedy_nmm_postprocess:
    match_metric: IOS
    class_agnostic: false

  yolo_labels:
    "0": Pedestrian
    "1": Bicycle
    "2": Vehicle
    "3": Motorbike
    "4": Airplane
    "5": Bus
    "6": Train
    "7": Truck
    "8": Boat

unified_tracking_config:
  max_track_age: 10
  association_config:
    appearance_weight: 0.4
    iou_weight: 0.6
    iou_threshold: 0.1
    similarity_threshold: 0.5

2d_tracking_config:
  similarity_threshold: 0.4
  max_disappeared: 5
  min_hits: 3
  motion_weight: 0.6
  appearance_weight: 0.4

3d_tracking_config:
  min_score_threshold: 
    vehicle: 0.12
    pedestrian: 0.4
  dthresh: 0.1
  mal: 2
  mah: 2
  min_hits: 3
  l2_thresh: 5
  cat: VEHICLE
  set: val
  keep_age: 3
  svel: 0.3
  sdis: 2
