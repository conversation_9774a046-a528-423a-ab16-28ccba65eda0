#!/usr/bin/env python3
"""
stack_rerun_vis.py – visualise the Stack-style dataset with Rerun.
"""
from __future__ import annotations

import argparse
import json
import pathlib
import re
import os
from typing import Any, List, Optional, Tuple

import cv2
import numpy as np
import open3d as o3d
import rerun as rr
import rerun.blueprint as rrb
from collections import Counter
from Rerun import Rerun          #  <--  import YOUR abstract base-class


from utils import config         # <-- import your config module
from utils import logger         # <-- import your logger module
from utils import logger_params  # <-- import your logger params



def _read_json(p: pathlib.Path) -> Any:
    with p.open("r") as f:
        return json.load(f)


def _bbox_to_min_size(bbox: List[float]) -> Tuple[List[float], List[float]]:
    """Convert [xmin, ymin, xmax, ymax] → mins, sizes for rr.Boxes2D."""
    xmin, ymin, xmax, ymax = bbox
    return [xmin, ymin], [xmax - xmin, ymax - ymin]


def _is_point_in_cuboid(point: np.ndarray, center: np.ndarray, half_size: np.ndarray, yaw: float) -> bool:
    """
    Check if a 3D point is inside a rotated 3D bounding box.

    Args:
        point: The point to check [x, y, z]
        center: Center of the cuboid [x, y, z]
        half_size: Half-sizes of the cuboid [dx/2, dy/2, dz/2]
        yaw: Rotation around z-axis in radians

    Returns:
        bool: True if the point is inside the cuboid
    """
    # Translate point to the cuboid's local coordinate system
    translated = point - center

    # Rotate point back (opposite of cuboid's rotation)
    cos_yaw, sin_yaw = np.cos(-yaw), np.sin(-yaw)
    rot_mat = np.array([
        [cos_yaw, -sin_yaw, 0],
        [sin_yaw, cos_yaw, 0],
        [0, 0, 1]
    ])

    local_point = rot_mat @ translated

    # Check if the point is inside the axis-aligned box in local coordinates
    return (
        np.abs(local_point[0]) <= half_size[0] and
        np.abs(local_point[1]) <= half_size[1] and
        np.abs(local_point[2]) <= half_size[2]
    )


class BoschVisualizer(Rerun):
    """Concrete dataset wrapper around your Stack GT/LiDAR format."""

    def __init__(self, data_dir: str, gt_dir: str):
        super().__init__(data_dir)
        self.gt_dir = pathlib.Path(gt_dir)
        self.rrd_path = os.path.join(config.pipeline_settings.output_dir, "rerun_visualization")
        os.makedirs(self.rrd_path, exist_ok=True)
        self._gt_paths: List[pathlib.Path] = []
        self._gt_cache: List[Optional[Any]] = []  # 1-to-1 with _gt_paths

    # --------------------------------------------------------------------- Custom visualize method

    def visualize(self, max_frames: int = None, fps: float = 10.0):
        """
        Custom visualization with colored points inside 3D bounding boxes.

        This implementation overrides the base class visualize method to add
        custom coloring for points inside bounding boxes.
        """
        # A) Gather files
        logger.info("Gathering files for visualization", **logger_params)
        lidar_files = self.gather_lidar_files()
        gt_files = self.gather_gt_files()
        camera_dirs = self.gather_camera_folders()

        if max_frames is not None:
            lidar_files = lidar_files[:max_frames]
            gt_files = gt_files[:max_frames]

        # Pair them index-based
        n = min(len(lidar_files), len(gt_files))
        frames = [(lidar_files[i], gt_files[i]) for i in range(n)]

        # B) Build blueprint
        logger.info("Building blueprint for visualization", **logger_params)
        camera_names = [pathlib.Path(d).name for d in camera_dirs]
        blueprint = self.build_blueprint(camera_names)

        # C) Setup Rerun
        logger.info("Setting up Rerun for visualization", **logger_params)
        parser = argparse.ArgumentParser()
        rr.init("stack_production", recording_id=f"{self.data_dir.name}", default_blueprint=blueprint)
        rr.script_add_args(parser)
        rrd_path = os.path.join(self.rrd_path, f"bosch_poc.rrd")
        rr.save(rrd_path, default_blueprint=blueprint)
        args = parser.parse_args()


        # D) Loop frames
        for i, (lidar_f, gt_f) in enumerate(frames):
            t_sec = i / fps
            rr.set_time_seconds("timestamp", t_sec)
            points = self.load_lidar_points(lidar_f)
            gt_data = self.load_gt(gt_f)
            colors = np.zeros((len(points), 4), dtype=np.uint8)
            colors[:, 2] = 255  # Set blue channel
            colors[:, 3] = 255  # Set alpha channel
            inside_color = np.array([255, 255, 0, 255], dtype=np.uint8)  # RGBA: Yellow

            # Get bounding box information
            for obj in gt_data["objects"]:
                if obj["3d_detection"]:
                    x, y, z, l, w, h, yaw = obj["3d_detection"]["cuboid"]
                    center = np.array([x, y, z])
                    half_size = np.array([l/2, w/2, h/2])

                    # Check each point if it's inside this bounding box
                    for j, point in enumerate(points):
                        if _is_point_in_cuboid(point, center, half_size, yaw):
                            colors[j] = inside_color

            # 4) Log colored points
            logger.debug(f"Logging LiDAR points for frame {i}", **logger_params)
            rr.log("world/lidar", rr.Points3D(points, colors=colors))

            # 5) Log 3D bounding boxes
            logger.debug(f"Logging 3D bounding boxes for frame {i}", **logger_params)
            self.log_3d_cuboids(gt_data, "world/lidar")

            # 6) Log camera images
            logger.debug(f"Logging camera images for frame {i}", **logger_params)
            for cam_dir in camera_dirs:
                img_path = self.gather_camera_images_for_frame(i, cam_dir)
                if img_path is not None:
                    img = self.load_image(img_path)
                    if img is not None:
                        camera_entity = f"world/cameras/{pathlib.Path(cam_dir).name}"
                        # Log 2D bounding boxes
                        self.log_2d_bboxes(gt_data, pathlib.Path(cam_dir).name, camera_entity, img)

    def gather_gt_files(self) -> List[str]:
        if not self._gt_paths:
            self._gt_paths = sorted(self.gt_dir.glob("*.json"),
                                key=lambda x: x.name.split("_")[-1])

            self._gt_cache = [None] * len(self._gt_paths)
        return [str(p) for p in self._gt_paths]

    def gather_lidar_files(self) -> List[str]:
        # One LiDAR per GT (taken from the “RefLidar” entry).
        lidar_paths: List[str] = []
        for gt_fp in self.gather_gt_files():
            gt = _read_json(pathlib.Path(gt_fp))
            lidar_rel = gt["frame_info"]["file_paths"]["RefLidar"]
            lidar_paths.append(str((self.data_dir / lidar_rel).resolve()))
        return lidar_paths

    def gather_camera_folders(self) -> List[str]:
        # camera names are the keys in the first GT's file_paths (minus RefLidar)
        first_gt = _read_json(pathlib.Path(self.gather_gt_files()[0]))
        cams = list(first_gt["frame_info"]["file_paths"].keys())
        cams.remove("RefLidar")
        return cams  # already just the names we want

    def load_lidar_points(self, lidar_file: str) -> np.ndarray:
        pcd = o3d.io.read_point_cloud(lidar_file)
        return np.asarray(pcd.points, dtype=np.float32)

    def load_gt(self, gt_file: str) -> Any:
        idx = self.gather_gt_files().index(gt_file)
        if self._gt_cache[idx] is None:
            self._gt_cache[idx] = _read_json(pathlib.Path(gt_file))

        return self._gt_cache[idx]

    def load_image(self, img_path: str) -> Optional[np.ndarray]:
        img = cv2.imread(str(img_path), cv2.IMREAD_COLOR)
        if img is not None:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # Rerun expects RGB
        return img

    def log_3d_cuboids(self, gt_data: dict, entity_prefix: str) -> None:
        centers, half_sizes, rotations = [], [], []
        labels = []

        for obj in gt_data["objects"]:
            if obj['3d_detection']:
                x, y, z, l, w, h, yaw = obj["3d_detection"]["cuboid"]
                centers.append([x, y, z])
                half_sizes.append([l / 2, w / 2, h / 2])
                rotations.append(rr.datatypes.RotationAxisAngle(
                    axis=[0, 0, 1],
                    angle=rr.datatypes.Angle(rad=float(yaw)),
                ))
                # labels.append(f"{obj["3d_detection"]["class"]}_{obj["object_id"]}")  # <-- keep the string
                labels.append(f"{obj['3d_detection']['class']}_{obj['object_id']}")
        rr.log(
            f"{entity_prefix}/cuboids",
            rr.Boxes3D(
                centers=centers,
                half_sizes=half_sizes,
                rotations=rotations,
                labels=labels,
            )                           
        )

    def gather_camera_images_for_frame(self, frame_idx: int,
                                       camera_name: str) -> Optional[str]:
        gt = self.load_gt(self.gather_gt_files()[frame_idx])
        rel_path = gt["frame_info"]["file_paths"].get(camera_name)
        if rel_path:
            full = (self.data_dir / rel_path).resolve()
            return str(full)
        return None

    def get_label_from_2d_projections(self, projections):
        all_labels = []
        for camera_name, proj_list in projections.items():
            for proj in proj_list:
                all_labels.append(proj['label'])

        counter = Counter(all_labels)
        most_common, count = counter.most_common(1)[0]
        return most_common




    def log_2d_bboxes(self, gt_data: Any, camera_name: str,
                      camera_entity: str, image: np.ndarray) -> None:
        # 1) log the image tensor itself …
        rr.log(camera_entity, rr.Image(image))

        # 2) … then two optional flavours of boxes
        mins_gt, sizes_gt = [], []
        mins_proj, sizes_proj = [], []
        labels = []

        for obj in gt_data["objects"]:
            proj_list = obj["2d_projections"].get(camera_name, [])
            for box_entry in proj_list:
                # Everything in “2d_projections” is a *projection* of the 3-D cuboid
                m, s = _bbox_to_min_size(box_entry["bbox"])
                mins_proj.append(m)
                sizes_proj.append(s)
                # labels.append(f"{obj["3d_detection"]["class"]}_{obj["object_id"]}")  # <-- keep the string
                if obj['3d_detection']:
                    labels.append(f"{obj['3d_detection']['class']}_{obj['object_id']}")
                else:
                    class_name_2d = self.get_label_from_2d_projections(obj['2d_projections'])
                    labels.append(f"{class_name_2d}_{obj['object_id']}")

        if mins_gt:
            rr.log(f"{camera_entity}/bbox/gt",
                   rr.Boxes2D(mins=mins_gt, sizes=sizes_gt))

        if mins_proj:
            rr.log(f"{camera_entity}/bbox/proj",
                   rr.Boxes2D(mins=mins_proj, sizes=sizes_proj, labels=(labels))
)

def main() -> None:
    """Main function to run the visualization script."""
    # A) Parse command-line arguments
    
    # data_dir = "data/bosch_rfi_sample_data/batch_1/"
    data_dir = config.pipeline_settings.seq_dir  # Directory containing .png / .pcd
    
    # gt_dir = "data/bosch_rfi_sample_data/batch_1/outputs/combined_output"
    gt_dir = os.path.join(config.pipeline_settings.seq_dir, "outputs/combined_output")  # Directory containing GT .json files
    max_frames = None
    fps = 1
    vis = BoschVisualizer(data_dir, gt_dir)
    vis.visualize(max_frames=max_frames, fps=fps)


if __name__ == "__main__":
    main()
