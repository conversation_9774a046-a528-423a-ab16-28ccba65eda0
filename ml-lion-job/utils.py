"""Global utility module."""
import logging
import os
import sys
from logging.handlers import TimedRotatingFileHandler

import omegaconf

APP_CONFIG = 'conf/app.yaml'
config = omegaconf.OmegaConf.load(APP_CONFIG)

logger = logging.getLogger(config.app_name)
logger.setLevel(int(config.log.level))
formatter = logging.Formatter(
    config.log.format,
)

stream_handler = logging.StreamHandler(stream=sys.stdout)
stream_handler.setLevel(int(config.log.level))
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

logger_params = {
    'extra': {
        'rid': 0,
        'host': config.host,
    },
}

if config.log.save_log_file:
    if not os.path.exists(os.path.dirname(config.log.file)):
        os.makedirs(os.path.dirname(config.log.file))
    file_handler = TimedRotatingFile<PERSON>andler(
        filename=config.log.file,
        when=config.log.rotation_mode,
        interval=config.log.interval,
        backupCount=config.log.backup_count,
    )
    file_handler.setLevel(int(config.log.level))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
import threading  # Add this import for thread-safe access
multiframe_task_status_lock = threading.Lock()
MULTIFRAME_TASK_STATUS = {}