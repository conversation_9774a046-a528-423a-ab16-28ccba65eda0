FROM nvidia/cuda:11.8.0-cudnn8-devel-ubuntu20.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    python3-pip \
    python3-dev \
    build-essential \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /LION

# Clone LION repository
RUN git clone https://github.com/happinesslz/LION.git .

RUN pip install --upgrade pip setuptools wheel packaging typing_extensions
RUN pip3 install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu118
# Install Python dependencies

RUN sed -i '/typing_extensions==4\.10\.0/d' requirements.txt
RUN sed -i '/numpy==/d' requirements.txt
RUN pip3 install -r requirements.txt
RUN pip3 install laspy
RUN pip3 install spconv-cu118
RUN python3 -m pip install causal-conv1d==1.2.0.post2
RUN pip3 install --upgrade typing_extensions
ENV TORCH_CUDA_ARCH_LIST="6.0 6.1 7.0 7.5 8.0 8.6+PTX"
RUN TORCH_CUDA_ARCH_LIST="${TORCH_CUDA_ARCH_LIST}" pip3 install -e .

WORKDIR /LION/pcdet/ops/mamba
RUN python3 setup.py install
RUN pip3 install .

# Set up environment variables for NVIDIA runtime
ENV NVIDIA_VISIBLE_DEVICES all
ENV NVIDIA_DRIVER_CAPABILITIES compute,utility


COPY requirements.txt /LION/requirements.txt
RUN pip3 install -r /LION/requirements.txt
RUN pip3 install -U numpy
RUN pip3 install av2==0.2.1
RUN pip3 install kornia==0.6.11
RUN pip3 install open3d
RUN pip3 uninstall -y torch-scatter
RUN pip3 install torch-scatter -f https://data.pyg.org/whl/torch-2.1.0+cu118.html

COPY requirements-rfi.txt /LION/requirements-rfi.txt
RUN pip3 install -r /LION/requirements-rfi.txt

WORKDIR /LION

ENV PYTHONPATH=/LION:/usr/lib/python3.8/site-packages/mamba_ssm-1.1.1-py3.8-linux-x86_64.egg

COPY app /LION/app
COPY conf /LION/conf
COPY src /LION/src
COPY tools /LION/tools
COPY pcdet /LION/pcdet
COPY run.sh /LION/run.sh
COPY utils.py /LION/utils.py
CMD ["sh","run.sh"]