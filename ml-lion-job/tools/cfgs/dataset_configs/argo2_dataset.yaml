BACKUP_DB_INFO:
  DB_INFO_PATH: 'falthu_path'
DATASET: 'Argo2Dataset'
DATA_PATH: '../data/argo2'

POINT_CLOUD_RANGE: [-200, -200, -20, 200, 200, 20]

DATA_SPLIT: {
    'train': train,
    'test': val
}

INFO_PATH: {
    'train': [argo2_infos_train.pkl],
    'test': [argo2_infos_val.pkl],
}

GET_ITEM_LIST: ["points"]

DATA_AUGMENTOR:
    BACKUP_DB_INFO:
      DB_INFO_PATH: 'falthu_path'
    DISABLE_AUG_LIST: ['placeholder']
    AUG_CONFIG_LIST:

        - NAME: random_world_flip
          ALONG_AXIS_LIST: ['x']

        - NAME: random_world_rotation
          WORLD_ROT_ANGLE: [-0.78539816, 0.78539816]

        - NAME: random_world_scaling
          WORLD_SCALE_RANGE: [0.95, 1.05]


POINT_FEATURE_ENCODING: {
    encoding_type: absolute_coordinates_encoding,
    used_feature_list: ['x', 'y', 'z', 'intensity'],
    src_feature_list: ['x', 'y', 'z', 'intensity'],
}


DATA_PROCESSOR:
    - NAME: mask_points_and_boxes_outside_range
      REMOVE_OUTSIDE_BOXES: True

    - NAME: shuffle_points
      SHUFFLE_ENABLED: {
        'train': True,
        'test': False
      }

    - NAME: transform_points_to_voxels
      VOXEL_SIZE: [0.1, 0.1, 0.25]
      MAX_POINTS_PER_VOXEL: 5
      MAX_NUMBER_OF_VOXELS: {
        'train': 160000,
        'test': 400000
      }
