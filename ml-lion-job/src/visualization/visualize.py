"""
Visualization utilities for displaying 3D-2D detection mappings.
"""

import os
import cv2
import numpy as np
import json
from pathlib import Path


def visualize_matches(mapping, file_mapping, output_dir='mapping_results'):
    """
    Visualize 3D-2D detection matches for each camera in a single image.
    
    Args:
        mapping (dict): Mapping between 3D and 2D detections
        file_mapping (dict): Mapping of stream names to file paths
        output_dir (str): Directory to save visualization results
    """
    from ..projection import CuboidProjector
    
    os.makedirs(output_dir, exist_ok=True)
    projector = CuboidProjector()
    
    # Group by camera
    camera_images = {}
    
    # First, load all camera images
    for camera_name in file_mapping:
        if camera_name == 'RefLidar':  # Skip LiDAR files
            continue
            
        image_path = file_mapping[camera_name]
        image = cv2.imread(image_path)
        
        if image is not None:
            camera_images[camera_name] = {
                'image': image.copy(),
                'path': image_path
            }
    
    # Define colors for different cuboids
    colors = [
        (0, 255, 0),    # <PERSON>
        (255, 0, 0),    # <PERSON>
        (0, 0, 255),    # <PERSON>
        (255, 255, 0),  # <PERSON><PERSON>
        (255, 0, 255),  # Magenta
        (0, 255, 255),  # Yellow
        (128, 128, 0),  # <PERSON>
        (128, 0, 128),  # Purple
        (0, 128, 128),  # Teal
        (255, 165, 0)   # Orange
    ]
    
    # Process each 3D detection and draw it on the appropriate camera image
    for idx, (cuboid_id, info) in enumerate(mapping.items()):
        cuboid = info['3d_detection']['cuboid']
        cuboid_class = info['3d_detection']['class']
        color = colors[idx % len(colors)]  # Assign a color to this cuboid
        
        for camera_name, matches in info['matches'].items():
            if camera_name not in camera_images:
                continue
                
            # Draw projected 3D cuboid with the assigned color
            camera_data = camera_images[camera_name]
            image = camera_data['image']
            
            # Project the 3D cuboid to the image
            try:
                # Get camera calibration
                intrinsic_data, cam_ext = projector.get_camera_calibration(camera_name)
                
                # Get 3D corners of the cuboid
                corners_3d = projector.cuboid_to_corners(cuboid)
                
                # Project corners to 2D
                corners_2d = []
                for corner in corners_3d:
                    try:
                        corner_2d = projector.project_point_to_image(corner, intrinsic_data, cam_ext)
                        corners_2d.append(corner_2d)
                    except Exception as e:
                        corners_2d.append(None)
                
                # Draw the cuboid edges
                edges = [
                    (0, 1), (1, 2), (2, 3), (3, 0),  # Bottom face
                    (4, 5), (5, 6), (6, 7), (7, 4),  # Top face
                    (0, 4), (1, 5), (2, 6), (3, 7)   # Vertical edges
                ]
                
                for start_idx, end_idx in edges:
                    if (corners_2d[start_idx] is not None and 
                        corners_2d[end_idx] is not None):
                        
                        start_point = tuple(map(int, corners_2d[start_idx]))
                        end_point = tuple(map(int, corners_2d[end_idx]))
                        
                        cv2.line(image, start_point, end_point, color, 2)
                
                # Add 3D class label near the cuboid
                if any(corners_2d):
                    valid_corners = [c for c in corners_2d if c is not None]
                    if valid_corners:
                        # Get center point of the valid corners
                        center_x = int(np.mean([c[0] for c in valid_corners]))
                        center_y = int(np.mean([c[1] for c in valid_corners]))
                        
                        # Add 3D class label
                        label_text = f"3D: {cuboid_class} (ID: {cuboid_id})"
                        cv2.putText(image, label_text, (center_x, center_y - 15), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                # Draw matching 2D detections with the same color
                for match in matches:
                    bbox = match['bbox']
                    x_min, y_min, x_max, y_max = map(int, bbox)
                    
                    # Use the same color as the 3D cuboid
                    cv2.rectangle(image, (x_min, y_min), (x_max, y_max), color, 2)  # Changed from (0,0,255) to color
                    
                    # Add label if available with matching color
                    if 'label' in match and 'score' in match:
                        label_text = f"2D: {match['label']} {match['score']:.2f}"
                        cv2.putText(image, label_text, (x_min, y_min-10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)  # Changed thickness and color
            
            except Exception as e:
                print(f"Error visualizing cuboid {cuboid_id} on {camera_name}: {e}")
    
    # Save one image per camera with all detections
    for camera_name, camera_data in camera_images.items():
        image = camera_data['image']
        image_basename = os.path.basename(camera_data['path']).split('.')[0]
        output_path = os.path.join(output_dir, f"{image_basename}_all_detections.jpg")
        cv2.imwrite(output_path, image)
        print(f"Saved visualization to {output_path}")


def visualize_detections(json_file, input_dir, output_dir):
    """
    Visualize 2D cuboid projections and detection rectangles from detection mapping JSON.
    
    Args:
        json_file (str): Path to the detection mapping JSON file
        input_dir (str): Directory containing the original images
        output_dir (str): Directory to save visualization results
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the JSON data
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Get frame info and file paths
    frame_info = data.get('frame_info', {})
    file_paths = frame_info.get('file_paths', {})
    
    # Define colors for different classes
    class_colors = {
        'Vehicle': (0, 255, 0),      # Green
        'Pedestrian': (0, 0, 255),   # Red
        'Cyclist': (255, 0, 0),      # Blue
        'Truck': (255, 255, 0),      # Cyan
        'Bus': (255, 0, 255),        # Magenta
        'default': (0, 255, 255)     # Yellow
    }
    
    # Process each camera
    for camera_name, image_filename in file_paths.items():
        if camera_name == 'RefLidar':  # Skip LiDAR files
            continue
        
        # Load the image
        image_path = os.path.join(input_dir, image_filename)
        if not os.path.exists(image_path):
            print(f"Warning: Image file {image_path} not found.")
            continue
        
        image = cv2.imread(image_path)
        if image is None:
            print(f"Warning: Could not load image {image_path}.")
            continue
        
        # Create a copy of the image for drawing
        vis_image = image.copy()
        
        # Process each object
        for obj in data['objects']:
            # Skip if no 2D projections or no projections for this camera
            if '2d_projections' not in obj or camera_name not in obj['2d_projections']:
                continue
            
            # Get object info
            object_id = obj['object_id']
            obj_class = obj['3d_detection']['class']
            color = class_colors.get(obj_class, class_colors['default'])
            
            # Process each match for this camera
            for match in obj['2d_projections'][camera_name]:
                # 1. Draw the 2D cuboid projection (connect the corners)
                corners_2d = match.get('corners_2d', [])
                valid_corners = [c for c in corners_2d if c is not None]
                
                if valid_corners:
                    # Draw cuboid edges - define the connections between corners
                    edges = [
                        (0, 1), (1, 2), (2, 3), (3, 0),  # Bottom face
                        (4, 5), (5, 6), (6, 7), (7, 4),  # Top face
                        (0, 4), (1, 5), (2, 6), (3, 7)   # Vertical edges
                    ]
                    
                    for start_idx, end_idx in edges:
                        if (start_idx < len(corners_2d) and end_idx < len(corners_2d) and 
                            corners_2d[start_idx] is not None and corners_2d[end_idx] is not None):
                            
                            start_point = tuple(map(int, corners_2d[start_idx]))
                            end_point = tuple(map(int, corners_2d[end_idx]))
                            
                            cv2.line(vis_image, start_point, end_point, color, 2)
                
                # 2. Draw the 2D detection rectangle
                bbox = match.get('bbox', [])
                if len(bbox) == 4:
                    x_min, y_min, x_max, y_max = map(int, bbox)
                    cv2.rectangle(vis_image, (x_min, y_min), (x_max, y_max), (0, 0, 255), 2)  # Red for 2D bbox
                    
                    # Add label and confidence if available
                    label_text = f"ID: {object_id}, Class: {obj_class}"
                    if 'confidence' in match:
                        label_text += f", {match['confidence']:.2f}"
                    
                    cv2.putText(vis_image, label_text, (x_min, y_min-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                    
                    # Add IoU information
                    if 'iou' in match:
                        iou_text = f"IoU: {match['iou']:.2f}"
                        cv2.putText(vis_image, iou_text, (x_min, y_min-30), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # Save the visualization
        output_filename = f"{Path(image_filename).stem}_visualization.jpg"
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, vis_image)
        print(f"Saved visualization to {output_path}")
