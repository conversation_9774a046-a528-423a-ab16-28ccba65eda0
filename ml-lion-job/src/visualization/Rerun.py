#!/usr/bin/env python3
from __future__ import annotations

import abc
import math
from typing import List, Dict, Optional, Any, <PERSON>ple
import pathlib
import rerun as rr
import rerun.blueprint as rrb
import numpy as np
import argparse
import re

class Rerun(abc.ABC):
    """
    Abstract base class to define the structure of a Rerun-based dataset visualization.

    Each dataset must implement the abstract methods to gather, load, and log data.
    The base 'visualize()' method does the main loop, building a blueprint and calling
    derived-class methods at the appropriate times.
    """

    def __init__(self, data_dir: str):
        """
        data_dir: path to the dataset's top-level directory.
        """
        self.data_dir = pathlib.Path(data_dir)

    #
    # Abstract methods: must be implemented by the derived class
    #

    # @abc.abstractmethod
    def gather_lidar_files(self) -> List[str]:
        """
        Return a sorted list of LiDAR file paths (e.g. .las or .bin).
        """
        pass

    # @abc.abstractmethod
    def gather_camera_folders(self) -> List[str]:
        """
        Return a list of camera folder paths or names for 2D images.
        """
        pass

    def parse_timestamp(self, filename: str) -> int:
        """
        Extracts the first numeric sequence from the filename stem.
        
        Raises:
            ValueError: If no number is found in the filename.
        """
        stem = pathlib.Path(filename).stem
        match = re.search(r"\d+", stem)
        if match:
            return int(match.group())
        else:
            raise ValueError(f"No numeric timestamp found in filename: {filename}")

    # @abc.abstractmethod
    def gather_gt_files(self) -> List[str]:
        """
        Return a sorted list of ground truth (JSON) file paths.
        """
        pass

    # @abc.abstractmethod
    def load_lidar_points(self, lidar_file: str) -> np.ndarray:
        """
        Load Nx3 float array from the LiDAR file.
        """
        pass

    # @abc.abstractmethod
    def load_gt(self, gt_file: str) -> Any:
        """
        Parse the ground-truth for a single frame (returns data for 2D/3D boxes).
        """
        pass

    # @abc.abstractmethod
    def log_3d_cuboids(self, gt_data: Any, entity_prefix: str) -> None:
        """
        Given parsed GT data, log 3D bounding boxes under e.g. "world/lidar/cuboids".
        """
        pass

    # @abc.abstractmethod
    def gather_camera_images_for_frame(self, frame_idx: int, camera_folder: str) -> Optional[str]:
        """
        For the i-th frame, return the path to the corresponding camera image
        in 'camera_folder', or None if none.
        This can be index-based, timestamp-based, etc.
        """
        pass

    # @abc.abstractmethod
    def load_image(self, img_path: str) -> Optional[np.ndarray]:
        """
        Load the camera image from disk using e.g. OpenCV.
        """
        pass

    # @abc.abstractmethod
    def log_2d_bboxes(self, gt_data: Any, camera_name: str, camera_entity: str, image: np.ndarray) -> None:
        """
        Log 2D bounding boxes for this camera.
        You should log the boxes into the camera entity, e.g. "world/cameras/camera_name/boxes".
        """

    #
    # Optionally, you can also override how you build the blueprint, etc.
    #

    def build_blueprint(self, camera_views: List[str]) -> rrb.Blueprint:
        """
        Build the Rerun blueprint: 3D LiDAR plus grid of 2D camera views.
        'camera_views' is a list of camera folder names we want to place in the blueprint.
        """
        s2d_views = []
        for name in camera_views:
            s2d_views.append(
                rrb.Spatial2DView(
                    name=name,
                    origin=f"world/cameras/{name}",
                    contents=["$origin/**"]
                )
            )

        blueprint = rrb.Blueprint(
            rrb.Vertical(
                rrb.Spatial3DView(name="3D LiDAR", origin="world", contents=["$origin/**"]),
                rrb.Grid(*s2d_views),
                row_shares=[1, 8],
            ),
            rrb.TimePanel(state="expanded"),
        )
        return blueprint

    def visualize(self, max_frames: int = None, fps: float = 10.0):
        """
        Main method to do the entire Rerun visualization loop.

        1) Gather LiDAR & GT files
        2) Build blueprint
        3) Log data frame-by-frame
        """

        # A) Gather files
        lidar_files = self.gather_lidar_files()
        gt_files    = self.gather_gt_files()
        camera_dirs = self.gather_camera_folders()
        # TODO: add gathering of calibration files

        if max_frames is not None:
            lidar_files = lidar_files[:max_frames]
            gt_files    = gt_files[:max_frames]
            # TODO: also limit camera_dirs

        # Pair them index-based
        # TODO: this should also have calibration files
        n = min(len(lidar_files), len(gt_files))
        frames = [(lidar_files[i], gt_files[i]) for i in range(n)]
        

        # B) Build blueprint
        camera_names = [pathlib.Path(d).name for d in camera_dirs]
        blueprint = self.build_blueprint(camera_names)

        # C) Setup Rerun
        parser = argparse.ArgumentParser()
        rr.script_add_args(parser)
        args = parser.parse_args()
        rr.script_setup(args, "BaseRerunVisualization", default_blueprint=blueprint)

        # D) Loop frames
        for i, (lidar_f, gt_f) in enumerate(frames):
            t_sec = i / fps
            rr.set_time_seconds("timestamp", t_sec)

            # 1) LiDAR
            points = self.load_lidar_points(lidar_f)
            rr.log("world/lidar", rr.Points3D(points))

            # 2) GT => 3D boxes
            gt_data = self.load_gt(gt_f)
            self.log_3d_cuboids(gt_data, "world/lidar",)

            # 3) Cameras
            for cam_dir in camera_dirs:
                img_path = self.gather_camera_images_for_frame(i, cam_dir)
                if img_path is not None:
                    img = self.load_image(img_path)
                    if img is not None:
                        camera_entity = f"world/cameras/{pathlib.Path(cam_dir).name}"
                        # log 2D bounding boxes
                        self.log_2d_bboxes(gt_data, pathlib.Path(cam_dir).name, camera_entity, img)
                        # TODO: ####log the projected image in 3d space ####
                        # modify the image by adding lidar points
                            #
                        # project the lidar points into the particular camera
                        # draw using open CV/rr.Boxes2D
                        # project the points from gt for that camera view and draw them as well

             

        rr.script_teardown(args)

