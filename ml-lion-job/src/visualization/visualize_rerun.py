#!/usr/bin/env python3
"""
stack_rerun_vis.py – visualise the Stack-style dataset with Rerun.
(Optimized Version)
"""
from __future__ import annotations

import argparse
import json
import pathlib
from typing import Any, List, Optional, Tuple

import cv2
import numpy as np
import open3d as o3d
import rerun as rr
import rerun.blueprint as rrb
from rerun.datatypes import <PERSON><PERSON>, RotationAxisAngle
from collections import Counter
from Rerun import Rerun  # <-- import YOUR abstract base-class


# ──────────────────────────────────────────────────────────────────────────────
# helper ──────────


def _read_json(p: pathlib.Path) -> Any:
    with p.open("r") as f:
        return json.load(f)


def _bbox_to_min_size(bbox: List[float]) -> Tuple[List[float], List[float]]:
    """Convert [xmin, ymin, xmax, ymax] → mins, sizes for rr.Boxes2D."""
    xmin, ymin, xmax, ymax = bbox
    return [xmin, ymin], [xmax - xmin, ymax - ymin]


def _is_point_in_cuboid_vectorized(points: np.ndarray, center: np.ndarray, half_size: np.ndarray, yaw: float) -> np.ndarray:
    """
    Check which 3D points are inside a rotated 3D bounding box using vectorized operations.

    Args:
        points: The points to check, shape (N, 3)
        center: Center of the cuboid [x, y, z]
        half_size: Half-sizes of the cuboid [dx/2, dy/2, dz/2]
        yaw: Rotation around z-axis in radians

    Returns:
        np.ndarray: A boolean array of shape (N,) where True means the point is inside.
    """
    # Translate points to the cuboid's local coordinate system
    translated_points = points - center  # Shape: (N, 3)

    # Create the inverse rotation matrix
    cos_yaw, sin_yaw = np.cos(-yaw), np.sin(-yaw)
    # Use the same dtype as points for efficiency
    rot_mat_inv = np.array([
        [cos_yaw, -sin_yaw, 0],
        [sin_yaw,  cos_yaw, 0],
        [0,        0,       1]
    ], dtype=points.dtype)

    # Rotate all points into the cuboid's local frame
    # (rot_mat_inv @ translated_points.T) -> (3, 3) @ (3, N) -> (3, N)
    # .T -> (N, 3)
    local_points = (rot_mat_inv @ translated_points.T).T

    # Check if points are within the axis-aligned box in the local frame
    # This comparison uses broadcasting and returns a boolean array of shape (N, 3)
    is_within_bounds = np.abs(local_points) <= half_size

    # A point is inside only if it's within bounds on all 3 axes
    # np.all(axis=1) reduces the (N, 3) array to a (N,) boolean mask
    return np.all(is_within_bounds, axis=1)


# ──────────────────────────────────────────────────────────────────────────────
# concrete implementation ──────────


class StackVisualizer(Rerun):
    """Concrete dataset wrapper around your Stack GT/LiDAR format."""

    def __init__(self, data_dir: str, gt_dir: str):
        super().__init__(data_dir)
        self.gt_dir = pathlib.Path(gt_dir)
        # The base `visualize()` calls our abstract methods many times,
        # so we keep small caches to avoid re-parsing the same JSON twice.
        self._gt_paths: List[pathlib.Path] = []
        self._gt_cache: List[Optional[Any]] = []  # 1-to-1 with _gt_paths

    # --------------------------------------------------------------------- Custom visualize method

    def visualize(self, max_frames: int = None, fps: float = 10.0):
        """
        Custom visualization with colored points inside 3D bounding boxes.

        This implementation overrides the base class visualize method to add
        custom coloring for points inside bounding boxes.
        """
        # A) Gather files
        lidar_files = self.gather_lidar_files()
        gt_files = self.gather_gt_files()
        camera_dirs = self.gather_camera_folders()

        if max_frames is not None:
            lidar_files = lidar_files[:max_frames]
            gt_files = gt_files[:max_frames]

        # Pair them index-based
        n = min(len(lidar_files), len(gt_files))
        frames = [(lidar_files[i], gt_files[i]) for i in range(n)]

        # B) Build blueprint
        camera_names = [pathlib.Path(d).name for d in camera_dirs]
        blueprint = self.build_blueprint(camera_names)

        # C) Setup Rerun
        parser = argparse.ArgumentParser()
        rr.script_add_args(parser)
        args = parser.parse_args()
        rr.script_setup(args, "StackVisualization", default_blueprint=blueprint)

        # D) Loop frames
        for i, (lidar_f, gt_f) in enumerate(frames):
            t_sec = i / fps
            rr.set_time_seconds("timestamp", t_sec)

            # 1) Load LiDAR points
            points = self.load_lidar_points(lidar_f)

            # 2) Load GT data
            gt_data = self.load_gt(gt_f)

            # 3) OPTIMIZED: Find points inside bounding boxes using vectorization
            # Initialize colors: default is blue (RGBA)
            colors = np.array([0, 0, 255, 255], dtype=np.uint8)
            colors = np.tile(colors, (len(points), 1))

            # Create a single boolean mask for all points that are inside *any* box
            points_inside_any_box = np.zeros(len(points), dtype=bool)

            for obj in gt_data["objects"]:
                if obj["3d_detection"]:
                    x, y, z, l, w, h, yaw = obj["3d_detection"]["cuboid"]
                    center = np.array([x, y, z])
                    half_size = np.array([l / 2, w / 2, h / 2])

                    # Get a mask for points inside the current box
                    is_inside_current_box = _is_point_in_cuboid_vectorized(points, center, half_size, yaw)

                    # Update the master mask
                    points_inside_any_box |= is_inside_current_box

            # Apply yellow color to all points inside any box in one operation
            inside_color = np.array([255, 255, 0, 255], dtype=np.uint8)  # RGBA: Yellow
            colors[points_inside_any_box] = inside_color

            # 4) Log colored points
            rr.log("world/lidar", rr.Points3D(points, colors=colors))

            # 5) Log 3D bounding boxes
            self.log_3d_cuboids(gt_data, "world/lidar")

            # 6) Log camera images
            for cam_dir in camera_dirs:
                img_path = self.gather_camera_images_for_frame(i, cam_dir)
                if img_path is not None:
                    img = self.load_image(img_path)
                    if img is not None:
                        camera_entity = f"world/cameras/{pathlib.Path(cam_dir).name}"
                        # Log image and 2D bounding boxes
                        self.log_2d_bboxes(gt_data, pathlib.Path(cam_dir).name, camera_entity, img)

        rr.script_teardown(args)


    # --------------------------------------------------------------------- gatherers

    def gather_gt_files(self) -> List[str]:
        if not self._gt_paths:
            self._gt_paths = sorted(self.gt_dir.glob("*.json"),
                                    key=lambda x: x.name.split("_")[-1])

            self._gt_cache = [None] * len(self._gt_paths)
        return [str(p) for p in self._gt_paths]

    def gather_lidar_files(self) -> List[str]:
        # One LiDAR per GT (taken from the “RefLidar” entry).
        lidar_paths: List[str] = []
        for gt_fp in self.gather_gt_files():
            gt = _read_json(pathlib.Path(gt_fp))
            lidar_rel = gt["frame_info"]["file_paths"]["RefLidar"]
            lidar_paths.append(str((self.data_dir / lidar_rel).resolve()))
        return lidar_paths

    def gather_camera_folders(self) -> List[str]:
        # camera names are the keys in the first GT's file_paths (minus RefLidar)
        first_gt = _read_json(pathlib.Path(self.gather_gt_files()[0]))
        cams = list(first_gt["frame_info"]["file_paths"].keys())
        cams.remove("RefLidar")
        return cams  # already just the names we want

    # --------------------------------------------------------------------- loaders

    def load_lidar_points(self, lidar_file: str) -> np.ndarray:
        pcd = o3d.io.read_point_cloud(lidar_file)
        return np.asarray(pcd.points, dtype=np.float32)

    def load_gt(self, gt_file: str) -> Any:
        idx = self.gather_gt_files().index(gt_file)
        if self._gt_cache[idx] is None:
            self._gt_cache[idx] = _read_json(pathlib.Path(gt_file))

        return self._gt_cache[idx]

    def load_image(self, img_path: str) -> Optional[np.ndarray]:
        img = cv2.imread(str(img_path), cv2.IMREAD_COLOR)
        if img is not None:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # Rerun expects RGB
        return img

    # --------------------------------------------------------------------- core logging

    def log_3d_cuboids(self, gt_data: dict, entity_prefix: str) -> None:
        centers, half_sizes, rotations = [], [], []
        labels = []

        for obj in gt_data["objects"]:
            if obj['3d_detection']:
                x, y, z, l, w, h, yaw = obj["3d_detection"]["cuboid"]
                centers.append([x, y, z])
                half_sizes.append([l / 2, w / 2, h / 2])
                rotations.append(rr.datatypes.RotationAxisAngle(
                    axis=[0, 0, 1],
                    angle=rr.datatypes.Angle(rad=float(yaw)),
                ))
                labels.append(f"{obj['3d_detection']['class']}_{obj['object_id']}")


        rr.log(
            f"{entity_prefix}/cuboids",
            rr.Boxes3D(
                centers=centers,
                half_sizes=half_sizes,
                rotations=rotations,
                labels=labels,
            )
        )

    def gather_camera_images_for_frame(self, frame_idx: int,
                                       camera_name: str) -> Optional[str]:
        gt = self.load_gt(self.gather_gt_files()[frame_idx])
        rel_path = gt["frame_info"]["file_paths"].get(camera_name)
        if rel_path:
            full = (self.data_dir / rel_path).resolve()
            return str(full)
        return None

    def get_label_from_2d_projections(self, projections):
        all_labels = []
        for camera_name, proj_list in projections.items():
            for proj in proj_list:
                all_labels.append(proj['label'])

        if not all_labels:
            return "unknown" # Handle case with no labels
        counter = Counter(all_labels)
        most_common, count = counter.most_common(1)[0]
        return most_common

    def log_2d_bboxes(self, gt_data: Any, camera_name: str,
                      camera_entity: str, image: np.ndarray) -> None:
        # 1) log the image tensor itself …
        rr.log(camera_entity, rr.Image(image))

        # 2) … then two optional flavours of boxes
        mins_gt, sizes_gt = [], []
        mins_proj, sizes_proj = [], []
        labels = []

        for obj in gt_data["objects"]:
            proj_list = obj["2d_projections"].get(camera_name, [])
            for box_entry in proj_list:
                # Everything in “2d_projections” is a *projection* of the 3-D cuboid
                m, s = _bbox_to_min_size(box_entry["bbox"])
                mins_proj.append(m)
                sizes_proj.append(s)
                if obj.get('3d_detection'):
                    labels.append(f"{obj['3d_detection']['class']}_{obj['object_id']}")
                else:
                    class_name_2d = self.get_label_from_2d_projections(obj['2d_projections'])
                    labels.append(f"{class_name_2d}_{obj['object_id']}")

        if mins_proj:
            rr.log(f"{camera_entity}/bbox/proj",
                   rr.Boxes2D(mins=mins_proj, sizes=sizes_proj, labels=labels)
)

# ──────────────────────────────────────────────────────────────────────────────
# main entry-point ──────────


def main() -> None:
    # Define paths directly instead of using arguments
    data_dir = "C:\\Users\\<USER>\\Desktop\\experimental_vis\\ext_benchmark_batch_3658182"  # Directory containing .png / .pcd
    gt_dir = "C:\\Users\\<USER>\\Desktop\\experimental_vis\\2d_tracking_json_output"    # Directory containing GT .json files
    max_frames = None
    fps = 11

    vis = StackVisualizer(data_dir, gt_dir)
    vis.visualize(max_frames=max_frames, fps=fps)


if __name__ == "__main__":
    main()