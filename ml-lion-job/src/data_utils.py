"""Module to download files from s3."""
import os
import re
import os
import hashlib
from botocore.exceptions import ClientError
import boto3

# Import logger utilities
from utils import logger
from utils import logger_params

BASE_KEY = ''
S3_BUCKET_NAME = f'imerit-ml-data-{os.environ.get("ENVIRONMENT")}'


def download_dir(base_key, bucket, prefix, local, ext=None):
    """
    Download model dir.

    params:
    - prefix: pattern to match in s3
    - local: local path to folder in which to place files
    - bucket: s3 bucket with target contents
    - client: initialized s3 client object
    """
    s3 = boto3.client('s3')
    keys = []
    dirs = []
    next_token = ''
    base_kwargs = {
        'Bucket': bucket,
        'Prefix': prefix,
    }
    logger.info(base_kwargs, **logger_params)
    while next_token is not None:
        kwargs = base_kwargs.copy()
        if next_token != '':
            kwargs.update({'ContinuationToken': next_token})
        results = s3.list_objects_v2(**kwargs)
        contents = results.get('Contents')
        for i in contents:
            k = i.get('Key')
            if k[-1] != '/':
                keys.append(k)
            else:
                dirs.append(k)
        next_token = results.get('NextContinuationToken', None)
    for k in keys:
        if ext is not None and k.endswith(ext) != True:
            continue
        dest_pathname = os.path.join(
            local, re.sub(
                # flake8: noqa
                re.sub(r'^data/', '', local), '', re.sub(base_key, '', k),
            ),
        )
        logger.info(f"downloading file from {k} to path {dest_pathname}", **logger_params)
        if not os.path.exists(os.path.dirname(dest_pathname)):
            os.makedirs(os.path.dirname(dest_pathname))
        s3.download_file(bucket, k, dest_pathname)


def download_s3_file(s3_bucket, s3_key_path, local_path):
    """Download file from s3."""
    local_dir_path = os.path.dirname(os.path.normpath(local_path))
    if not os.path.exists(local_dir_path):
        try:
            logger.info(f'directory {local_dir_path} does not exist, creating...', **logger_params)
            os.makedirs(local_dir_path)
        except FileExistsError:
            pass
    s3 = boto3.client('s3')
    try:
        logger.info(f"downloading file {s3_key_path} to path {local_path}", **logger_params)
        if not os.path.exists(os.path.dirname(local_path)):
            os.makedirs(os.path.dirname(local_path))
        s3.download_file(s3_bucket, s3_key_path, local_path)
    except Exception as ex:
        logger.info(f"{s3_bucket}/{s3_key_path} file could not be downloaded", **logger_params)
        raise ex


def upload_s3_file(local_path, s3_bucket, s3_key):
    s3 = boto3.client('s3')
    try:
        logger.info(f'Uploading file {local_path} to s3 location bucket={s3_bucket},key={s3_key}', **logger_params)
        s3.upload_file(local_path, s3_bucket, s3_key)
    except Exception as ex:
        logger.info(f'Failed to upload file {local_path}', **logger_params)
        raise ex


def download_s3_folder(s3_key_path, local_data_path, base_key=BASE_KEY, s3_bucket_name=S3_BUCKET_NAME,  ext=None):
    """Download folder from s3."""
    logger.info(f"downloading folder from {s3_key_path} to path {local_data_path}", **logger_params)
    download_dir(base_key, s3_bucket_name, s3_key_path, local_data_path, ext)


def get_file(data_path, base_key=BASE_KEY, s3_bucket_name=S3_BUCKET_NAME, use_local=True):
    """Get file."""
    if os.path.exists(data_path):
        if use_local:
            logger.info(f"===> found local copy <=== {data_path}", **logger_params)
            return
        else:
            os.remove(data_path)

    logger.info(f"file: {data_path} doesn't exist locally. Downloading...", **logger_params)
    s3_key_path = base_key + re.sub(r'^data/', '', data_path)
    logger.info(f"key: {s3_key_path}", **logger_params)
    if s3_key_path.endswith("/"):
        download_s3_folder(s3_key_path, data_path, base_key, s3_bucket_name)
    else:
        download_s3_file(s3_bucket_name, s3_key_path, data_path)


def requires_data(*args, **kwargs):
    """Download the required files or folders."""
    def inner(func):
        files = kwargs['files']
        use_local = kwargs.get('use_local', True)
        base_key = kwargs.get('base_key', BASE_KEY)
        s3_bucket_name = kwargs.get('s3_bucket', S3_BUCKET_NAME)
        for file_path in files:
            logger.info(f'Getting file {file_path}', **logger_params)
            get_file(file_path, base_key, s3_bucket_name=s3_bucket_name, use_local=use_local)
        return func

    return inner


def parse_s3_url(s3_url):
    """
    Parses an S3 URL to extract the bucket name and key.

    :param s3_url: The S3 URL (e.g., 's3://bucket-name/key/path/to/object').
    :return: A tuple containing the bucket name and key.
    """
    # Regular expression to match the S3 URL
    match = re.match(r's3://([^/]+)/(.+)', s3_url)

    if match:
        bucket_name = match.group(1)
        key = match.group(2)
        return bucket_name, key
    else:
        raise ValueError("Invalid S3 URL")

def list_files_from_s3(s3_bucket, s3_key, ext=None):
    """
    List all files in an S3 bucket with the given key as prefix and optional file extension.

    :param s3_bucket: The name of the S3 bucket.
    :param s3_key: The prefix key in the S3 bucket.
    :param ext: Optional file extension to filter files.
    :return: A list of file keys matching the criteria.
    """
    s3 = boto3.client('s3')
    files = []
    next_token = ''
    base_kwargs = {
        'Bucket': s3_bucket,
        'Prefix': s3_key,
    }
    while next_token is not None:
        kwargs = base_kwargs.copy()
        if next_token != '':
            kwargs.update({'ContinuationToken': next_token})
        results = s3.list_objects_v2(**kwargs)
        contents = results.get('Contents', [])
        for obj in contents:
            key = obj.get('Key')
            if ext is None or key.endswith(ext):
                files.append(key)
        next_token = results.get('NextContinuationToken', None)
    return files

def check_s3_file_exists(s3_bucket, s3_key):
    """
    Check if a file exists in an S3 bucket.

    :param s3_bucket: The name of the S3 bucket.
    :param s3_key: The key of the file in the S3 bucket.
    :return: True if the file exists, False otherwise.
    """
    s3 = boto3.client('s3')
    try:
        s3.head_object(Bucket=s3_bucket, Key=s3_key)
        return True
    except Exception as e:
        return False

def calculate_md5(file_path):
    """Calculate the MD5 hash of a file."""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def s3_object_etag(bucket, key):
    """Get the ETag of an S3 object (if exists)."""
    s3 = boto3.client('s3')
    try:
        response = s3.head_object(Bucket=bucket, Key=key)
        etag = response["ETag"].strip('"')
        return etag
    except ClientError as e:
        if e.response['Error']['Code'] == "404":
            return None
        else:
            raise

def sync_to_s3(local_dir, bucket, s3_prefix=""):
    s3 = boto3.client('s3')
    for root, dirs, files in os.walk(local_dir):
        for filename in files:
            local_path = os.path.join(root, filename)
            relative_path = os.path.relpath(local_path, local_dir)
            s3_key = os.path.join(s3_prefix, relative_path).replace("\\", "/")

            local_md5 = calculate_md5(local_path)
            s3_etag = s3_object_etag(bucket, s3_key)

            if s3_etag == local_md5:
                logger.info(f"Skipping (unchanged): {relative_path}", **logger_params)
            else:
                s3.upload_file(local_path, bucket, s3_key)
                logger.info(f"Uploaded: {relative_path} to s3://{bucket}/{s3_key}", **logger_params)