import os
from glob import glob
import json
import numpy as np
import uuid
from datetime import datetime, timezone

def get_string(input):
    return f"{input}-{uuid.uuid4().hex[:8]}"

def read_json(json_path):
    """
    Read a JSON file.

    Parameters
    ----------
    json_path: str
        Path to the JSON file

    Returns
    -------
    dict
        Dictionary containing the JSON data
    """
    with open(json_path, "r") as f:
        data = json.load(f)
    return data

def yaw_to_quaternion(yaw_radians):
    """
    Convert yaw angle to quaternion (rotation around Z-axis)
    
    Args:
        yaw_radians: Yaw angle in radians
        
    Returns:
        tuple: (qx, qy, qz, qw) quaternion components
    """
    # For rotation around Z-axis only
    qx = 0.0
    qy = 0.0
    qz = np.sin(yaw_radians / 2.0)
    qw = np.cos(yaw_radians / 2.0)
    
    return (qx, qy, qz, qw)

def get_object_type(obj):
    if "3d_detection" in obj and obj["3d_detection"]:
        return obj["3d_detection"]["class"]
    elif "2d_projections" in obj:
        for cam, bbox_info in obj["2d_projections"].items():
            return bbox_info[0]["label"]

def remove_none_values_inplace(data):
    """Modify dictionary in-place to remove None values"""
    if isinstance(data, dict):
        keys_to_remove = [key for key, value in data.items() if value is None]
        for key in keys_to_remove:
            data.pop(key)

        for value in data.values():
            remove_none_values_inplace(value)

    elif isinstance(data, list):
        for i in range(len(data) - 1, -1, -1):
            if data[i] is None:
                data.pop(i)
            else:
                remove_none_values_inplace(data[i])

    return data

def create_bolf_json(ml_output_dir, input_json_folder, output_json):
    bolf_json = {}
    
    all_jsons = glob(f"{input_json_folder}/*.json")
    sample_json = all_jsons[0]
    data = read_json(sample_json)

    # coordinate_systems
    coordinate_systems = data["openlabel"]["coordinate_systems"]
    if "pose_wrt_parent" in coordinate_systems["vehicle-RearAxleCenterProjectedToGround"] and coordinate_systems["vehicle-RearAxleCenterProjectedToGround"]["pose_wrt_parent"] is None:
        coordinate_systems["vehicle-RearAxleCenterProjectedToGround"].pop("pose_wrt_parent")
    
    # metadata
    metadata = data["openlabel"]["metadata"]
    if "name" in metadata:
        metadata.pop("name")
    metadata["annotator"] = "imerit"
    metadata["comment"] = "imerit autolabelling solution"
    metadata["created"] = str(datetime.now(timezone.utc))

    metadata["schema_version"] =  '1.0.0'
    metadata["schema_variant_version"] = "3.1.0"
    metadata["schema_variant_uri"] = "bosch_openlabel_schema_v_3_1_0.json"
    metadata["format_variant"] = "label"

    metadata["contexts"] = {
                "batch": {
                    "name": "batch_info",
                    "type": "batch_context",
                    "context_data": {}
                },
                "label_request": {
                    "name": "label_request_info",
                    "type": "request_context",
                    "context_data": {}
                },
                "labeling": {
                    "name": "workpackages",
                    "type": "labeling_workpackages",
                    "context_data": {}
                }
            }


    
    # streams
    streams = data["openlabel"]["streams"]
    for cam, cam_info in streams.items():
        cam_info["uri"] = os.path.basename(input_json_folder)

    sub_elems = {
        "coordinate_systems": coordinate_systems,
        "metadata": metadata,
        "streams": streams
    }

    ml_jsons = os.listdir(ml_output_dir)
    sorted_ml_jsons = sorted(ml_jsons, key=lambda x: int(x.split("_")[-1].split(".")[0]))
    uuid_to_no = {}
    for ml_json in sorted_ml_jsons:
        split_keys = ml_json.split("_")
        uuid_to_no[split_keys[3]] = int(split_keys[4].split(".")[0])
    to_del = []
    for i,input_json in enumerate(all_jsons):
        if os.path.basename(input_json).split("_")[-1].split(".")[0] not in uuid_to_no:
            to_del.append(i)
    for i in reversed(to_del):
        all_jsons.pop(i)
    sorted_input_jsons = sorted(all_jsons, key=lambda x: uuid_to_no[os.path.basename(x).split("_")[-1].split(".")[0]])

    frame_to_info = {}
    object_info = {}
    object_id_to_uuid = {}
    for i, (ml_json, input_json) in enumerate(zip(sorted_ml_jsons, sorted_input_jsons)):
        ml_data = read_json(os.path.join(ml_output_dir, ml_json))
        input_data = read_json(input_json)
        frame_str = str(i)
        frame_to_info[frame_str] = {}
        # get frame properties from input json file
        frame_to_info[frame_str]["frame_properties"] = input_data["openlabel"]["frames"]["0"]["frame_properties"]
        for cam, cam_info in frame_to_info[frame_str]["frame_properties"]["streams"].items():
            if cam in ml_data["frame_info"]["file_paths"]:
                cam_info["uri"] = ml_data["frame_info"]["file_paths"][cam]
            else:
                cam_info["uri"] = "NA"

        frame_to_info[frame_str]["objects"] = {}

        for objects in ml_data["objects"]:
            object_id = objects["object_id"]
            object_dict = {}
            if object_id not in object_id_to_uuid:
                object_id_to_uuid[object_id] = str(uuid.uuid4())
                objetct_type = get_object_type(objects)
                object_info[object_id_to_uuid[object_id]] = {
                    "name": object_id_to_uuid[object_id],
                    "type": objetct_type,
                }

            frame_to_info[frame_str]["objects"][object_id_to_uuid[object_id]] = {}
            
            if objects["3d_detection"]:
                cuboid_info = objects["3d_detection"]
                position = cuboid_info["position"]
                dimensions = cuboid_info["dimensions"]
                quat_xyzw = yaw_to_quaternion(cuboid_info["yaw"])
                
                # cuboid x,y,z,qx,qy,qz,qw,l,w,h
                cuboid = [*position, *quat_xyzw, *dimensions]
                stream = "RefLidar"
                name = get_string("cuboid")
                object_dict["cuboid"] = [{
                    "name": name,
                    "val": cuboid,
                    "stream": stream
                }]

            if objects["2d_projections"]:
                object_dict["bbox"] = []
                for stream, bbox_info in objects["2d_projections"].items():
                    x_min, y_min, x_max, y_max = bbox_info[0]["bbox"]
                    x_center = (x_min + x_max) / 2
                    y_center = (y_min + y_max) / 2
                    val = [x_center, y_center, x_max - x_min, y_max - y_min]
                    name = get_string("bbox")
                    object_dict["bbox"].append(
                        {
                            "name": name,
                            "val": val, 
                            "stream": stream
                        }
                    )

            frame_to_info[frame_str]["objects"][object_id_to_uuid[object_id]]["object_data"] = object_dict

    sub_elems["frames"] = frame_to_info
    sub_elems["objects"] = object_info

    bolf_json["openlabel"] = sub_elems

    bolf_json = remove_none_values_inplace(bolf_json)

    with open(output_json, "w") as fout:
        json.dump(bolf_json, fout, indent=4)


