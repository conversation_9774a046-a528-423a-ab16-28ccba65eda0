"""
Utility for mapping files based on UUIDs in calibration JSON.
"""

import os
import json
from glob import glob
# Import logger utilities
from utils import logger
from utils import logger_params


def map_files_for_json(json_file_path, folder_path):
    """
    Map files in the folder to streams defined in the JSON file based on UUIDs.
    
    Args:
        json_file_path (str): Path to the calibration JSON file.
        folder_path (str): Path to the folder containing all files.
    
    Returns:
        dict: Mapping of stream names to filenames.
    """
    try:
        # Load JSON file
        with open(json_file_path, 'r') as f:
            calib_data = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.info(f"Error loading JSON file: {e}", **logger_params)
        return {}
    
    # Extract frame data
    try:
        frame_data = calib_data['openlabel']['frames']['0']
        frame_timestamp = frame_data['frame_properties']['timestamp']
        
        # Extract stream UUIDs
        stream_shas = {}
        for stream_name, stream_info in frame_data['frame_properties']['streams'].items():
            uuid = stream_info['stream_properties'].get('uuid', 'N/A')
            stream_shas[stream_name] = uuid
            
    except KeyError as e:
        logger.info(f"Error extracting frame data: {e}", **logger_params)
        return {}
    
    # Get all files in the folder
    all_files = glob(os.path.join(folder_path, '*'))
    
    # Initialize mapping for this JSON file
    file_mapping = {}
    
    # Process each stream
    for stream_name, uuid in stream_shas.items():
        for file_path in all_files:
            if file_path.endswith('.json'):
                continue
            file_basename = os.path.basename(file_path)
            
            if uuid in file_basename:
                file_mapping[stream_name] = os.path.join(folder_path, file_basename)
                break  # Only take the first matching file per stream
    
    return file_mapping
