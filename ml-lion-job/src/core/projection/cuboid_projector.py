"""
3D Cuboid projection utilities for projecting 3D bounding boxes to 2D image planes.
"""

import numpy as np
import cv2
# Import logger utilities
from utils import logger
from utils import logger_params


class CuboidProjector:
    """
    Class for projecting 3D cuboid bounding boxes to 2D camera images.
    """
    
    def __init__(self, calibration_data=None):
        """
        Initialize the CuboidProjector.
        
        Args:
            calibration_data (dict, optional): Dictionary containing calibration data.
        """
        self.data = calibration_data
    
    def set_calibration_data(self, calibration_data):
        """
        Set or update the calibration data.
        
        Args:
            calibration_data (dict): Dictionary containing calibration data.
        """
        self.data = calibration_data
    
    def get_camera_calibration(self, camera_name):
        """
        Extract camera calibration data for a specific camera.
        
        Args:
            camera_name (str): Name of the camera.
        
        Returns:
            tuple: (intrinsic_data, cam_ext) - Camera intrinsic parameters and extrinsic matrix.
        
        Raises:
            ValueError: If camera calibration data is not found.
        """
        if not self.data:
            raise ValueError("Calibration data not set. Use set_calibration_data() first.")
            
        camera_data = None
        for item in self.data['calibration']:
            if item['name'] == camera_name:
                camera_data = item
                break
        
        if not camera_data:
            raise ValueError(f"Camera calibration data for '{camera_name}' not found.")
        
        # Extract intrinsic data
        focal_length_x, focal_length_y = camera_data['intrinsic']['focal_length']
        principal_point_x, principal_point_y = camera_data['intrinsic']['principal_point']
        
        # Extract extrinsic matrix (4x4 from elements)
        extrinsic_elements = camera_data['extrinsic']['elements']
        extrinsic_matrix = np.array(extrinsic_elements).reshape(4, 4).T
        
        # Prepare the intrinsic data format
        intrinsic_data = {
            "principal_point": np.array([principal_point_x, principal_point_y]),
            "focal_length_x": focal_length_x,
            "focal_length_y": focal_length_y,
            "cut_angle_lower": camera_data['intrinsic'].get('cut_angle_lower', None),
            "cut_angle_upper": camera_data['intrinsic'].get('cut_angle_upper', None),
            "type": camera_data['intrinsic']['type']
        }
        
        # Invert the extrinsic matrix
        cam_ext = np.linalg.inv(extrinsic_matrix)
        
        return intrinsic_data, cam_ext
    
    def cuboid_to_corners(self, cuboid):
        """
        Convert cuboid [x,y,z,l,w,h,yaw] to 8 corner points in 3D space.
        
        Args:
            cuboid: [x, y, z, l, w, h, yaw] where:
                    x, y, z: center coordinates
                    l, w, h: length, width, height
                    yaw: rotation around z-axis in radians
        
        Returns:
            np.ndarray: 8x3 array of corner coordinates
        """
        x, y, z, l, w, h, yaw = cuboid
        
        # Define the 8 corners of a unit cube centered at origin
        # Bottom face (z = -h/2), Top face (z = +h/2)
        corners_local = np.array([
            [-l/2, -w/2, -h/2],  # 0: bottom-back-left
            [+l/2, -w/2, -h/2],  # 1: bottom-back-right
            [+l/2, +w/2, -h/2],  # 2: bottom-front-right
            [-l/2, +w/2, -h/2],  # 3: bottom-front-left
            [-l/2, -w/2, +h/2],  # 4: top-back-left
            [+l/2, -w/2, +h/2],  # 5: top-back-right
            [+l/2, +w/2, +h/2],  # 6: top-front-right
            [-l/2, +w/2, +h/2],  # 7: top-front-left
        ])
        
        # Create rotation matrix around z-axis
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        rotation_matrix = np.array([
            [cos_yaw, -sin_yaw, 0],
            [sin_yaw,  cos_yaw, 0],
            [0,        0,       1]
        ])
        
        # Apply rotation and translation
        corners_rotated = corners_local @ rotation_matrix.T
        corners_world = corners_rotated + np.array([x, y, z])
        
        return corners_world
    
    def project_point_to_image(self, point, intrinsic_data, cam_ext):
        """
        Project a single 3D point to 2D image coordinates.
        
        Args:
            point (np.ndarray): 3D point to project.
            intrinsic_data (dict): Camera intrinsic parameters.
            cam_ext (np.ndarray): Camera extrinsic matrix.
            
        Returns:
            np.ndarray: 2D image coordinates.
        """
        camera_type = intrinsic_data['type']
        
        if camera_type == 'cylindrical':
            return self.project_point_to_fc_image(point, intrinsic_data, cam_ext)
        elif camera_type == 'cylindrical/deformed':
            return self.project_point_to_tv_image(point, intrinsic_data, cam_ext)
        else:
            raise ValueError(f"Unsupported camera type: {camera_type}")
    
    def project_point_to_fc_image(self, point, fc_intrinsic_data, fc_cam_ext):
        """
        Project a single 3D point to 2D image coordinates for FC camera.
        
        Args:
            point (np.ndarray): 3D point to project.
            fc_intrinsic_data (dict): FC camera intrinsic parameters.
            fc_cam_ext (np.ndarray): FC camera extrinsic matrix.
            
        Returns:
            np.ndarray: 2D image coordinates.
        """
        point_3d_homogeneous = np.array([point[0], point[1], point[2], 1.0])
        point_cam = fc_cam_ext @ point_3d_homogeneous
        
        # Retrieve intrinsic parameters
        principal_focal_x = fc_intrinsic_data['principal_point'][0]
        principal_focal_y = fc_intrinsic_data['principal_point'][1]
        focal_length_x = fc_intrinsic_data['focal_length_x']
        focal_length_y = fc_intrinsic_data['focal_length_y']
        
        # Calculate 2D image coordinates
        x_2d = principal_focal_x - (focal_length_x * np.arctan2(point_cam[1], point_cam[0]))
        y_2d = principal_focal_y - ((focal_length_y * point_cam[2]) / np.sqrt(point_cam[0] ** 2 + point_cam[1] ** 2))
        
        return np.array([x_2d, y_2d])
    
    def project_point_to_tv_image(self, point, tv_intrinsic_data, tv_cam_ext):
        """
        Project a single 3D point to 2D image coordinates for TV camera.
        
        Args:
            point (np.ndarray): 3D point to project.
            tv_intrinsic_data (dict): TV camera intrinsic parameters.
            tv_cam_ext (np.ndarray): TV camera extrinsic matrix.
            
        Returns:
            np.ndarray: 2D image coordinates.
        """
        pt = np.array([point[0], point[1], point[2], 1.0])
        self.tv_intrinsic_data = tv_intrinsic_data
        self.tv_cam_ext = tv_cam_ext
        
        # Apply extrinsic matrix to point
        pt = np.dot(self.tv_cam_ext, pt)
        
        altitude_angle = np.degrees(np.arctan2(-pt[2], np.sqrt(pt[0] ** 2 + pt[1] ** 2)))
        
        m_cut_heights = self.determine_cut_heights()
        
        if altitude_angle > self.tv_intrinsic_data['cut_angle_lower']:
            return self.light_ray_sensor_to_pixel_equidistant(pt, self.tv_intrinsic_data['cut_angle_lower'],
                                                         m_cut_heights['m_lower'])
        elif altitude_angle < self.tv_intrinsic_data['cut_angle_upper']:
            return self.light_ray_sensor_to_pixel_equidistant(pt, self.tv_intrinsic_data['cut_angle_upper'],
                                                         m_cut_heights['m_upper'])
        else:
            return self.light_ray_sensor_to_pixel_cylinder(pt)
    
    def determine_cut_heights(self):
        """
        Determine cut heights for TV camera projection.
        
        Returns:
            dict: Dictionary containing upper and lower cut heights.
        """
        m_upper = np.tan(np.radians(self.tv_intrinsic_data['cut_angle_upper'])) * self.tv_intrinsic_data['focal_length_y']
        m_lower = np.tan(np.radians(self.tv_intrinsic_data['cut_angle_lower'])) * self.tv_intrinsic_data['focal_length_y']
        return {'m_upper': m_upper, 'm_lower': m_lower}
    
    def light_ray_sensor_to_pixel_equidistant(self, light_ray_vector, cut_angle, cut_height):
        """
        Convert a light ray vector to pixel coordinates using equidistant projection.
        
        Args:
            light_ray_vector (np.ndarray): The light ray vector.
            cut_angle (float): Cut angle in degrees.
            cut_height (float): Cut height.
            
        Returns:
            np.ndarray: 2D image coordinates.
        """
        principal_point_equidistant = self.tv_intrinsic_data['principal_point'] + np.array([0, cut_height])
        
        azimuth = np.arctan2(light_ray_vector[1], light_ray_vector[0])
        altitude = np.arctan(-light_ray_vector[2] / np.sqrt(light_ray_vector[0] ** 2 + light_ray_vector[1] ** 2))
        altitude_equidistant = altitude - np.arcsin(np.tan(np.radians(cut_angle)) * np.cos(altitude))
        
        light_ray_vector_equidistant = np.array([
            np.cos(altitude_equidistant) * np.cos(azimuth),
            np.cos(altitude_equidistant) * np.sin(azimuth),
            -np.sin(altitude_equidistant)
        ])
        
        radius_equidistant = np.linalg.norm(light_ray_vector_equidistant)
        if radius_equidistant == 0:
            raise ValueError("Unspecified error during transformation")
        
        light_ray_vector_equidistant_cv = np.array([
            -light_ray_vector_equidistant[1],
            -light_ray_vector_equidistant[2],
            light_ray_vector_equidistant[0]
        ])
        
        theta = np.arccos(light_ray_vector_equidistant_cv[2] / radius_equidistant)
        radius_circle = np.linalg.norm(light_ray_vector_equidistant_cv[:2])
        
        ratio = 1.0 if radius_circle == 0.0 else theta / radius_circle
        
        pixel_normalized = np.array([
            ratio * light_ray_vector_equidistant_cv[0],
            ratio * light_ray_vector_equidistant_cv[1]
        ])
        
        pixel_x_component = (pixel_normalized[0] * self.tv_intrinsic_data['focal_length_x']) + principal_point_equidistant[0]
        squeeze_factor = self.calculate_squeeze_factor_equidistant(pixel_x_component)
        
        return np.array([
            pixel_x_component,
            (pixel_normalized[1] * self.tv_intrinsic_data['focal_length_y'] * squeeze_factor) + principal_point_equidistant[1]
        ])
    
    def light_ray_sensor_to_pixel_cylinder(self, light_ray_vector):
        """
        Convert a light ray vector to pixel coordinates using cylindrical projection.
        
        Args:
            light_ray_vector (np.ndarray): The light ray vector.
            
        Returns:
            np.ndarray: 2D image coordinates.
        """
        return np.array([
            self.tv_intrinsic_data['principal_point'][0] - (
                self.tv_intrinsic_data['focal_length_x'] * np.arctan2(light_ray_vector[1], light_ray_vector[0])),
            self.tv_intrinsic_data['principal_point'][1] - (
                (self.tv_intrinsic_data['focal_length_y'] * light_ray_vector[2]) / np.sqrt(
                light_ray_vector[0] ** 2 + light_ray_vector[1] ** 2))
        ])
    
    def calculate_squeeze_factor_equidistant(self, pixel_coordinate_x):
        """
        Calculate the squeeze factor for equidistant projection.
        
        Args:
            pixel_coordinate_x (float): X coordinate in pixels.
            
        Returns:
            float: Squeeze factor.
        """
        pixel_ratio = 1.0 - (pixel_coordinate_x / self.tv_intrinsic_data['principal_point'][0])
        m_max_vertical_squeeze = 0.375
        return 1.0 - (m_max_vertical_squeeze * (pixel_ratio * pixel_ratio))
    
    def is_point_in_image(self, point_2d, image_shape):
        """
        Check if 2D point is within image boundaries.
        
        Args:
            point_2d (np.ndarray): 2D point coordinates.
            image_shape (tuple): Shape of the image (height, width).
            
        Returns:
            bool: True if point is in image, False otherwise.
        """
        h, w = image_shape[:2]
        x, y = point_2d
        return 0 <= x < w and 0 <= y < h
    
    def get_bounding_rectangle(self, points, image_dims):
        """
        Extract a bounding rectangle from projected cuboid points.
        
        Args:
            points (list): List of 2D points representing cuboid corners.
            
        Returns:
            tuple: (x_min, y_min, x_max, y_max) bounding rectangle.
        """
        # Convert to numpy array if not already
        points_array = np.array(points)
        width, height = image_dims
        # Find min and max coordinates
        x_min = max(np.min(points_array[:, 0]),0)
        y_min = max(np.min(points_array[:, 1]),0)
        x_max = min(np.max(points_array[:, 0]), width-1)
        y_max = min(np.max(points_array[:, 1]), height-1)
        
        # Return as [x_min, y_min, x_max, y_max]
        return (int(x_min), int(y_min), int(x_max), int(y_max))
    
    def project_cuboid_to_image(self, cuboid, camera_name, image_path, output_path=None, 
                               line_color=(0, 255, 0), line_thickness=2):
        """
        Project 3D cuboid to image and draw it.
        
        Args:
            cuboid: [x, y, z, l, w, h, yaw] - 3D cuboid parameters
            camera_name: Name of the camera ('FC1', 'TV_front', etc.)
            image_path: Path to the input image
            output_path: Path to save the output image (optional)
            line_color: Color of the cuboid lines in BGR format
            line_thickness: Thickness of the lines
        
        Returns:
            image_with_cuboid: Image with projected cuboid drawn
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        # Get camera calibration
        intrinsic_data, cam_ext = self.get_camera_calibration(camera_name)
        
        # Convert cuboid to 3D corners
        corners_3d = self.cuboid_to_corners(cuboid)
        
        # Project all corners to 2D
        corners_2d = []
        valid_corners = []
        
        for i, corner in enumerate(corners_3d):
            try:
                corner_2d = self.project_point_to_image(corner, intrinsic_data, cam_ext)
                corners_2d.append(corner_2d)
                # Check if point is within image bounds
                if self.is_point_in_image(corner_2d, image.shape):
                    valid_corners.append(i)
            except Exception as e:
                logger.info(f"Warning: Could not project corner {i}: {e}", **logger_params)
                corners_2d.append(None)
        
        # Draw the cuboid edges
        # Define the 12 edges of a cuboid (connecting the 8 corners)
        edges = [
            # Bottom face edges (0,1,2,3)
            (0, 1), (1, 2), (2, 3), (3, 0),
            # Top face edges (4,5,6,7)
            (4, 5), (5, 6), (6, 7), (7, 4),
            # Vertical edges connecting bottom and top
            (0, 4), (1, 5), (2, 6), (3, 7)
        ]
        
        # Draw edges
        for start_idx, end_idx in edges:
            if (corners_2d[start_idx] is not None and 
                corners_2d[end_idx] is not None):
                
                start_point = tuple(map(int, corners_2d[start_idx]))
                end_point = tuple(map(int, corners_2d[end_idx]))
                
                cv2.line(image, start_point, end_point, line_color, line_thickness)
        
        # Draw corner points for visualization
        for i, corner_2d in enumerate(corners_2d):
            if corner_2d is not None:
                center = tuple(map(int, corner_2d))
                cv2.circle(image, center, 3, line_color, -1)
        
        # Draw bounding rectangle if at least one corner is visible
        if any(corner_2d is not None for corner_2d in corners_2d):
            corners_2d_filtered = [c for c in corners_2d if c is not None]
            if corners_2d_filtered:
                x, y, x1, y1 = self.get_bounding_rectangle(corners_2d_filtered)
                cv2.rectangle(image, (x, y), (x1, y1), line_color, 2)
        
        # Save output image if path provided
        if output_path:
            cv2.imwrite(output_path, image)
        
        return image, corners_2d
    
    def project_multiple_cuboids(self, cuboids, camera_name, image_path, output_path=None, 
                                colors=None, line_thickness=2):
        """
        Project multiple 3D cuboids to image and draw them with different colors.
        
        Args:
            cuboids: List of [x, y, z, l, w, h, yaw] - 3D cuboid parameters
            camera_name: Name of the camera
            image_path: Path to the input image
            output_path: Path to save the output image (optional)
            colors: List of colors for each cuboid (BGR format)
            line_thickness: Thickness of the lines
        
        Returns:
            image_with_cuboids: Image with all projected cuboids drawn
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        # Default colors if not provided
        if colors is None:
            colors = [
                (0, 255, 0),    # Green
                (255, 0, 0),    # Blue
                (0, 0, 255),    # Red
                (255, 255, 0),  # Cyan
                (255, 0, 255),  # Magenta
                (0, 255, 255),  # Yellow
            ]
        
        # Get camera calibration
        intrinsic_data, cam_ext = self.get_camera_calibration(camera_name)
        
        # Process each cuboid
        for idx, cuboid in enumerate(cuboids):
            color = colors[idx % len(colors)]
            
            # Convert cuboid to 3D corners
            corners_3d = self.cuboid_to_corners(cuboid)
            
            # Project all corners to 2D
            corners_2d = []
            for corner in corners_3d:
                try:
                    corner_2d = self.project_point_to_image(corner, intrinsic_data, cam_ext)
                    corners_2d.append(corner_2d)
                except Exception as e:
                    corners_2d.append(None)
            
            # Draw the cuboid edges
            edges = [
                (0, 1), (1, 2), (2, 3), (3, 0),  # Bottom face
                (4, 5), (5, 6), (6, 7), (7, 4),  # Top face
                (0, 4), (1, 5), (2, 6), (3, 7)   # Vertical edges
            ]
            
            for start_idx, end_idx in edges:
                if (corners_2d[start_idx] is not None and 
                    corners_2d[end_idx] is not None):
                    
                    start_point = tuple(map(int, corners_2d[start_idx]))
                    end_point = tuple(map(int, corners_2d[end_idx]))
                    
                    cv2.line(image, start_point, end_point, color, line_thickness)
        
        # Save output image if path provided
        if output_path:
            cv2.imwrite(output_path, image)
        
        return image
