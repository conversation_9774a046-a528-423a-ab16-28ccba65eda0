"""
Calibration module for handling camera and LiDAR calibration data.
"""

import json
import numpy as np
from scipy.spatial.transform import Rotation as R


def read_json(file_path: str) -> dict:
    """
    Read and parse a JSON file.
    
    Args:
        file_path (str): Path to the JSON file.
        
    Returns:
        dict: Parsed JSON data.
    """
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error reading JSON file {file_path}: {e}")
        return {}


def calib_base_structure() -> dict:
    """
    Create a base structure for calibration data.
    
    Returns:
        dict: Empty calibration structure.
    """
    return {
        "name": "",
        "extrinsic": {},
        "intrinsic": {}
    }


def get_transformation_matrix(qx, qy, qz, qw, tx, ty, tz) -> np.ndarray:
    """
    Create a 4x4 transformation matrix from quaternion and translation.
    
    Args:
        qx, qy, qz, qw: Quaternion components.
        tx, ty, tz: Translation components.
        
    Returns:
        np.ndarray: 4x4 transformation matrix.
    """
    try:
        # Create rotation matrix from quaternion
        rotation = R.from_quat([qx, qy, qz, qw])
        rot_matrix = rotation.as_matrix()  # 3x3 rotation matrix

        # Create the 4x4 transformation matrix
        transformation_matrix = np.eye(4)
        transformation_matrix[:3, :3] = rot_matrix
        transformation_matrix[:3, 3] = [tx, ty, tz]

        return transformation_matrix
    except Exception as e:
        print(f"Error creating transformation matrix: {e}")
        return np.eye(4)


def calculate_extrinsic_intrinsics(quaternion_translation_info: dict, intrinsics_info: dict, 
                                  lidar_name: str) -> dict:
    """
    Calculate extrinsic and intrinsic parameters for all cameras.
    
    Args:
        quaternion_translation_info (dict): Dictionary containing quaternion and translation data.
        intrinsics_info (dict): Dictionary containing intrinsic parameters.
        lidar_name (str): Name of the reference LiDAR.
        
    Returns:
        dict: Calibration data with extrinsics and intrinsics.
    """
    try:
        calculation_calib_list = []
        
        calculation_calib_list.append({"name": lidar_name, "version": ""})
        
        for sensor_, intrin_value in intrinsics_info.items():
            
            base_structure = calib_base_structure()
            
            base_structure['name'] = sensor_
            base_structure['intrinsic'] = {
                i: k for i, k in intrin_value.items() 
                if i in ['type', 'focal_length', 'principal_point', 'cut_angle_upper', 'cut_angle_lower']
            }

            quaternion_ = quaternion_translation_info[sensor_]['quaternion']
            translation_ = quaternion_translation_info[sensor_]['translation']
            qt_params = quaternion_ + translation_
            
            transformation_matrix = get_transformation_matrix(
                qt_params[0], qt_params[1], qt_params[2], qt_params[3],
                qt_params[4], qt_params[5], qt_params[6]
            )
    
            base_structure['extrinsic']['elements'] = np.array(transformation_matrix).T.flatten().tolist()

            calculation_calib_list.append(base_structure)
        
        return {"calibration": calculation_calib_list}
    except Exception as e:
        print(f"Error calculating extrinsics/intrinsics: {e}")
        return {"calibration": []}


def get_calibration(raw_calib_file: str, lidar_name: str, camera_sensors: list) -> dict:
    """
    Extract calibration data from a JSON file.
    
    Args:
        raw_calib_file (str): Path to the calibration JSON file.
        lidar_name (str): Name of the reference LiDAR.
        camera_sensors (list): List of camera sensor names to extract.
        
    Returns:
        dict: Processed calibration data.
    """
    try:
        raw_calib_data = read_json(raw_calib_file)
        # Extract coordinate systems and streams
        coordinate_systems = raw_calib_data['openlabel']['coordinate_systems']
        streams = raw_calib_data['openlabel']['streams']
        
        # Extract quaternion and translation information for cameras
        quaternion_translation_info = {}
        children = camera_sensors[:5]
        for cam_sensor, cam_values in coordinate_systems.items():
            if cam_sensor not in children:
                continue
            if cam_sensor not in quaternion_translation_info:
                quaternion_translation_info[cam_sensor] = dict()
            quaternion_translation_info[cam_sensor] = {
                "quaternion": cam_values['pose_wrt_parent']['quaternion'],
                "translation": cam_values['pose_wrt_parent']['translation']
            }
        
        # Extract intrinsics information for cameras
        intrinsics_info = {}
        for cam_sensor, cam_values in streams.items():
            if cam_sensor not in children:
                continue
            if cam_sensor not in intrinsics_info:
                intrinsics_info[cam_sensor] = list()
            intrinsics_info[cam_sensor] = cam_values['stream_properties']['intrinsics']
        
        # Calculate extrinsics and intrinsics
        return calculate_extrinsic_intrinsics(quaternion_translation_info, intrinsics_info, lidar_name)
        
    except Exception as e:
        print(f"Error getting calibration data: {e}")
        return {"calibration": []}
