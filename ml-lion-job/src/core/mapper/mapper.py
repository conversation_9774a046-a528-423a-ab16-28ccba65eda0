"""
Module for mapping between 3D and 2D detections.
"""

import numpy as np
import cv2
import os
import json
from pathlib import Path
from ..projection import CuboidProjector
from scipy.optimize import linear_sum_assignment
# Import logger utilities
from utils import logger
from utils import logger_params


def calculate_iou(box1, box2):
    """
    Calculate IoU (Intersection over Union) between two bounding boxes.

    Args:
        box1 (tuple): (x_min, y_min, x_max, y_max)
        box2 (list): [x_min, y_min, x_max, y_max]

    Returns:
        float: IoU value
    """
    # Ensure boxes are in the same format
    x_min1, y_min1, x_max1, y_max1 = box1
    x_min2, y_min2, x_max2, y_max2 = box2

    # Calculate intersection coordinates
    x_min_intersect = max(x_min1, x_min2)
    y_min_intersect = max(y_min1, y_min2)
    x_max_intersect = min(x_max1, x_max2)
    y_max_intersect = min(y_max1, y_max2)

    # Check if there is an intersection
    if x_max_intersect < x_min_intersect or y_max_intersect < y_min_intersect:
        return 0.0

    # Calculate intersection area
    intersection_area = (x_max_intersect - x_min_intersect) * (y_max_intersect - y_min_intersect)

    # Calculate union area
    box1_area = (x_max1 - x_min1) * (y_max1 - y_min1)
    box2_area = (x_max2 - x_min2) * (y_max2 - y_min2)
    union_area = box1_area + box2_area - intersection_area

    # Calculate IoU
    iou = intersection_area / union_area if union_area > 0 else 0

    return iou

def linear_sum_assignment_dist(cost_matrix, dist_threshold=1e6):
    """
    Perform linear sum assignment on IoU matrix using Hungarian algorithm.
    Pre-filters rows/columns where max IoU < threshold before assignment.

    Args:
        iou_matrix: IoU matrix of shape (M, N)
        iou_threshold: Minimum IoU threshold for valid assignments

    Returns:
        matched_indices: List of (row_idx, col_idx) for valid matches
        unmatched_dets: List of unmatched detection indices
        unmatched_trks: List of unmatched track indices
    """
    if min(cost_matrix.shape) == 0:
        return [], list(range(cost_matrix.shape[0])), list(range(cost_matrix.shape[1])), []

    # Pre-filter: identify rows/columns with max IoU >= threshold
    valid_rows = np.where(np.min(cost_matrix, axis=1) < dist_threshold)[0]
    valid_cols = np.where(np.min(cost_matrix, axis=0) < dist_threshold)[0]

    # Initialize unmatched lists with all indices
    unmatched_dets = list(range(cost_matrix.shape[0]))
    unmatched_trks = list(range(cost_matrix.shape[1]))
    matched_indices = []
    iou_vals = []
    # If no valid rows or columns, return all as unmatched
    if len(valid_rows) == 0 or len(valid_cols) == 0:
        return matched_indices, unmatched_dets, unmatched_trks, []

    # Create filtered matrix with only valid rows/columns
    filtered_iou = cost_matrix[np.ix_(valid_rows, valid_cols)]

    # Perform Hungarian algorithm on filtered matrix
    row_indices, col_indices = linear_sum_assignment(filtered_iou)

    # Map back to original indices and filter by threshold
    for row_idx, col_idx in zip(row_indices, col_indices):
        orig_row = valid_rows[row_idx]
        orig_col = valid_cols[col_idx]
        matched_indices.append((orig_row, orig_col))
        unmatched_dets.remove(orig_row)
        unmatched_trks.remove(orig_col)
        iou_vals.append(filtered_iou[row_idx, col_idx])

    return matched_indices, unmatched_dets, unmatched_trks, iou_vals


def map_3d_to_2d_detections(file_mapping, detections_3d, detections_2d, projector, next_available_id):
    """
    Map 3D LiDAR detections to corresponding 2D camera detections.

    Args:
        file_mapping (dict): Mapping of stream names to file paths
        detections_3d (list): List of 3D detections
        detections_2d (dict): Dictionary of 2D detections for each camera
        projector (CuboidProjector): Initialized CuboidProjector with calibration data

    Returns:
        dict: Mapping between 3D and 2D detections
    """
    # Check if inputs are valid
    if not detections_3d:
        logger.info("No 3D detections found.", **logger_params)
        return {}

    if not detections_2d:
        logger.info("No 2D detections found.", **logger_params)
        return {}

    # Step 4: For each 3D detection, find corresponding 2D detections
    mapping = {}

    mapped_2d_detections = set()

    cost_matrix_dict = {}
    for camera_name, camera_data in detections_2d.items():
        if 'bboxes' in camera_data:
            cost_matrix_dict[camera_name] = np.zeros((len(detections_3d), len(camera_data['bboxes'])))


    # Process each 3D detection
    for idx_3d, det_3d in enumerate(detections_3d):
        cuboid_id = det_3d.get('id', idx_3d)
        mapping[cuboid_id] = {
            '3d_detection': det_3d,
            'matches': {}
        }

        # Extract cuboid parameters
        cx, cy, cz, dx, dy, dz, yaw = det_3d['cuboid']

        # For each camera with 2D detections
        for camera_name, camera_data in detections_2d.items():

            if camera_name not in file_mapping:
                continue

            image_path = file_mapping[camera_name]
            width, height = (6090, 3074) if camera_name =="FC1" else (1536, 1536)

            # Project 3D cuboid to 2D image
            try:
                # Get camera calibration
                intrinsic_data, cam_ext = projector.get_camera_calibration(camera_name)

                # Get 3D corners of the cuboid
                corners_3d = projector.cuboid_to_corners([cx, cy, cz, dx, dy, dz, yaw])

                # Project corners to 2D
                corners_2d = []
                for corner in corners_3d:
                    try:
                        corner_2d = projector.project_point_to_image(corner, intrinsic_data, cam_ext)
                        corners_2d.append(corner_2d.tolist())  # Convert numpy array to list for JSON serialization
                    except Exception as e:
                        logger.info(f"Warning: Could not project corner: {e}", **logger_params)
                        corners_2d.append(None)

                # Filter out None values
                corners_2d_filtered = [c for c in corners_2d if c is not None]
                if not corners_2d_filtered:
                    continue

                # Get the bounding rectangle
                proj_bbox = projector.get_bounding_rectangle(corners_2d_filtered, (width, height))

                # Check for overlaps with 2D detections
                if 'bboxes' in camera_data:
                    for idx_2d, bbox in enumerate(camera_data['bboxes']):
                        # Calculate IoU
                        iou = calculate_iou(proj_bbox, bbox)

                        if iou > 0.3:
                            cost_matrix_dict[camera_name][idx_3d, idx_2d] = np.linalg.norm(np.array([cx,cy,cz]))
                        else:
                            cost_matrix_dict[camera_name][idx_3d, idx_2d] = 1e6


            except Exception as e:
                logger.info(f"Error processing {camera_name} for 3D detection {cuboid_id}: {e}", **logger_params)
                continue

    for camera_name, camera_data in detections_2d.items():

        matched_indices, unmatched_2d_dets, unmatched_2d_dets, iou_vals = linear_sum_assignment_dist(cost_matrix_dict[camera_name], dist_threshold=1e6)


        for i, (idx_3d, idx_2d) in enumerate(matched_indices):
            matches = []
            cuboid_id = detections_3d[idx_3d]['id']
            bbox = camera_data['bboxes'][idx_2d]
            mapped_2d_detections.add((camera_name,idx_2d))
            match_info = {
                'bbox_idx': idx_2d,
                'iou': iou_vals[i],
                'bbox': bbox,
            }

            # Add score and label if available
            if 'scores' in camera_data and idx_2d < len(camera_data['scores']):
                match_info['score'] = camera_data['scores'][idx_2d]

            if 'labels' in camera_data and idx_2d < len(camera_data['labels']):
                match_info['label'] = camera_data['labels'][idx_2d]

            matches.append(match_info)


            if matches:
                mapping[cuboid_id]['matches'][camera_name] = matches



    for camera_name, camera_data in detections_2d.items():
        unmatched_list_camera = []

        if camera_name not in file_mapping:
            continue
        if 'bboxes' in camera_data:
            for idx_2d, bbox in enumerate(camera_data['bboxes']):
                if (camera_name,idx_2d) not in mapped_2d_detections:
                    mapping[next_available_id] = {
                        '3d_detection': {},
                        'matches': {
                            camera_name: [{'bbox_idx': idx_2d,
                                        'iou': 0,
                                        'bbox': bbox,
                                        'corners_2d': None,
                                        'score': camera_data['scores'][idx_2d],
                                        'label': camera_data['labels'][idx_2d],

                            }]
                        }
                    }
                    next_available_id += 1

    return mapping, next_available_id


def create_output_json(mapping, file_mapping, output_file='detection_mapping.json'):
    """
    Create and save a structured JSON output containing 3D detections and their 2D projections.

    Args:
        mapping (dict): The mapping between 3D and 2D detections
        file_mapping (dict): Mapping of stream names to file paths
        output_file (str): Path to save the output JSON file

    Returns:
        dict: The output structure that was saved to the JSON file
    """
    output = {
        "frame_info": {
            "file_paths": {k: os.path.basename(v) for k, v in file_mapping.items()},
            "timestamp": Path(file_mapping.get('RefLidar', '')).stem.split('_')[0] if file_mapping.get('RefLidar') else None
        },
        "objects": []
    }

    # Process each 3D detection and its matches
    for cuboid_id, info in mapping.items():
        # Extract basic 3D detection information
        if info['3d_detection']:
            cuboid = info['3d_detection']['cuboid']
            object_data = {
                "object_id": cuboid_id,
                "3d_detection": {
                    "class": info['3d_detection']['class'],
                    "position": cuboid[:3],  # x, y, z
                    "dimensions": cuboid[3:6],  # length, width, height
                    "yaw": cuboid[6],
                    "cuboid": cuboid  # Full cuboid parameters
                },
                "2d_projections": {}
            }
        else:
            object_data = {
                "object_id": cuboid_id,
                "3d_detection": {},
                "2d_projections": {}
            }

        # Add 2D projection information for each camera
        for camera_name, matches in info['matches'].items():
            camera_projections = []

            for match in matches:
                projection = {
                    "bbox": match['bbox'],  # [x_min, y_min, x_max, y_max]
                    "iou": match['iou'],
                }

                # Add label and confidence if available
                if 'label' in match:
                    projection["label"] = match['label']

                if 'score' in match:
                    projection["confidence"] = match['score']

                camera_projections.append(projection)

            object_data["2d_projections"][camera_name] = camera_projections

        output["objects"].append(object_data)

    # Save to JSON file
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(output, f, indent=2)

    logger.info(f"Mapping saved to {output_file}", **logger_params)
    return output
