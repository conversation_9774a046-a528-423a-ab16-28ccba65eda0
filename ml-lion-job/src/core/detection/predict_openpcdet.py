import glob
from pathlib import Path
import json,os
import laspy

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils
import open3d as o3d

# Import logger utilities
from utils import logger as custom_logger
from utils import logger_params

class DemoDataset(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, training=True, root_path=None, logger=None, ext='.bin', config=None):
        """
        Args:
            root_path:
            dataset_cfg:
            class_names:
            training:
            logger:
            config: Configuration dictionary for 3D detection settings
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        self.root_path = root_path
        self.ext = ext
        self.config = config or {}
        data_file_list = glob.glob(str(root_path / f'*{self.ext}')) if self.root_path.is_dir() else [self.root_path]

        data_file_list.sort()
        self.sample_file_list = data_file_list

    def __len__(self):
        return len(self.sample_file_list)

    def __getitem__(self, index):

        if self.ext == '.bin':
            # points = np.fromfile(self.sample_file_list[index], dtype=np.float32).reshape(-1, 4)
            points = np.fromfile(self.sample_file_list[index], dtype=np.float32).reshape(-1, 4)[:,:4]

        elif self.ext == '.npy':
            points = np.load(self.sample_file_list[index])
        elif self.ext == '.pcd':    
            pcd = o3d.io.read_point_cloud(self.sample_file_list[index])
                
                # Get points as numpy array
            points = np.asarray(pcd.points)   
            default_intensity = self.config.get('default_intensity_pcd', 127)
            intensity = np.full((points.shape[0], 1), default_intensity) 
            
            # Concatenate the original points with intensity column
            points = np.hstack([points, intensity])
         
        else:
            self.ext=='.las'
            las = laspy.read(self.sample_file_list[index])
            if self.config.get('log_intensity_range', False):
                custom_logger.info(f"{np.min(np.array(las.intensity))} {np.max(np.array(las.intensity))}", **logger_params)
            points = np.stack((las.x, las.y, las.z, las.intensity),axis=1)
        base_name = os.path.basename(self.sample_file_list[index])
        file_name, self.ext = os.path.splitext(base_name)
        input_dict = {
            'points': points,
            'frame_id': index,
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        data_dict['file_name']= file_name

        return data_dict



def run_3d_inference(data_path, extn, model_path, cfg_path, output_folder, config=None):
    """
    Run 3D inference on point cloud data.
    
    Args:
        data_path: Path to the data directory
        extn: File extension of point cloud files
        model_path: Path to the model weights
        cfg_path: Path to the model configuration file
        output_folder: Output directory for results
        config: Configuration dictionary for 3D detection settings
    """
    config = config or {}
    cfg_from_yaml_file(cfg_path, cfg)
    logger = common_utils.create_logger()
    logger.info('-----------------Quick Demo of OpenPCDet-------------------------')
    demo_dataset = DemoDataset(
        dataset_cfg=cfg.DATA_CONFIG, class_names=cfg.CLASS_NAMES, training=False,
        root_path=Path(data_path), ext=extn, logger=logger, config=config
    )
    logger.info(f'Total number of samples: \t{len(demo_dataset)}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=model_path, logger=logger, to_cpu=True)
    
    # Use configurable device
    device = config.get('device', 'cuda')
    if device == 'cuda' and torch.cuda.is_available():
        model.cuda()
    elif device == 'cpu':
        model.cpu()
    else:
        model.cuda()  # fallback to cuda
        
    model.eval()
    with torch.no_grad():
        for idx, data_dict in enumerate(demo_dataset):
            logger.info(f'Visualized sample index: \t{idx + 1}')
            file_name=data_dict.pop('file_name')
            file_info={}
            file_info['file_name']=file_name
            file_info['lidar_path']=f"{data_path}/{file_name}{extn}"
            file_info['extn']=extn
            
            points = data_dict['points']
            data_dict = demo_dataset.collate_batch([data_dict])
            load_data_to_gpu(data_dict)
            pred_dicts, _ = model.forward(data_dict)


            # Convert tensors to lists
            for item in pred_dicts:
                item['pred_boxes'] = item['pred_boxes'].tolist()
                item['pred_scores'] = item['pred_scores'].tolist()
                item['pred_labels'] = item['pred_labels'].tolist()

            os.makedirs(f'{output_folder}',exist_ok=True)
            with open(f"{output_folder}/{file_name}.json", "w") as f:
                json.dump(pred_dicts, f, indent=4)

            custom_logger.info(f"JSON file saved as {output_folder}/{file_name}.json", **logger_params)
