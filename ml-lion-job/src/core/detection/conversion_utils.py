import numpy as np
import torch
import json


class NumpyEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that handles NumPy arrays and other non-serializable types.
    """
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, torch.Tensor):
            return obj.cpu().detach().numpy().tolist()
        return super(NumpyEncoder, self).default(obj)

def to_serializable(obj):
    """
    Convert an object to a JSON serializable format.
    
    Args:
        obj: Any Python object
        
    Returns:
        JSON serializable version of the object
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, torch.Tensor):
        return obj.cpu().detach().numpy().tolist()
    elif isinstance(obj, dict):
        return {k: to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list) or isinstance(obj, tuple):
        return [to_serializable(item) for item in obj]
    else:
        return obj

