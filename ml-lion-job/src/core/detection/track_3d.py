from __future__ import print_function
import os
import os.path, copy, numpy as np, time, sys
from scipy.optimize import linear_sum_assignment
from filterpy.kalman import <PERSON><PERSON><PERSON><PERSON><PERSON>
from scipy.spatial import ConvexHull
import uuid
import json
import argparse
from shapely.geometry import Polygon
# Import logger utilities
from utils import logger
from utils import logger_params

# Default configuration
DEFAULT_CONFIG = {
    'dthresh': 0.1,
    'mal': 2,
    'mah': 2,
    'min_hits': 3,
    'l2_thresh': 5,
    'cat': "VEHICLE",
    'set': "val",
    'keep_age': 3,
    'svel': 0.3,
    'sdis': 2
}

def load_config(config_path=None, config_dict=None):
    """
    Load configuration from file or dictionary

    Parameters
    ----------
    config_path: str, optional
        Path to JSON config file
    config_dict: dict, optional
        Configuration dictionary

    Returns
    -------
    dict
        Configuration dictionary with default values filled in
    """
    config = DEFAULT_CONFIG.copy()

    if config_path:
        with open(config_path, 'r') as f:
            user_config = json.load(f)
        config.update(user_config)
    elif config_dict:
        config.update(config_dict)

    return config

def get_corners_2d(x, y, l, w, yaw):
    """
    Compute the 2D bounding box corners given center (x, y), dimensions (l, w), and rotation (yaw).
    """
    dx = l / 2
    dy = w / 2

    # Define the corner points before rotation
    corners = np.array([
        [dx, dy],
        [-dx, dy],
        [-dx, -dy],
        [dx, -dy]
    ])

    # Rotate around center
    rotation_matrix = np.array([
        [np.cos(yaw), -np.sin(yaw)],
        [np.sin(yaw), np.cos(yaw)]
    ])

    rotated_corners = np.dot(corners, rotation_matrix.T) + np.array([x, y])

    return rotated_corners

def compute_2d_iou(box1, box2):
    """
    Compute 2D IoU of two rotated bounding boxes.
    """
    poly1 = Polygon(get_corners_2d(*box1))
    poly2 = Polygon(get_corners_2d(*box2))

    if not poly1.is_valid or not poly2.is_valid:
        return 0.0

    inter_area = poly1.intersection(poly2).area
    union_area = poly1.area + poly2.area - inter_area

    return inter_area

def compute_3d_iou(box1, box2):
    """
    Compute 3D IoU between two oriented bounding boxes.
    Each box is represented as (x, y, z, l, w, h, yaw).
    """
    x1, y1, z1, l1, w1, h1, yaw1 = box1
    x2, y2, z2, l2, w2, h2, yaw2 = box2

    # Compute 2D IoU
    inter_2d = compute_2d_iou((x1, y1, l1, w1, yaw1), (x2, y2, l2, w2, yaw2))

    # Compute the intersection in Z-axis
    z_top1, z_bottom1 = z1 + h1 / 2, z1 - h1 / 2
    z_top2, z_bottom2 = z2 + h2 / 2, z2 - h2 / 2

    inter_z = max(0, min(z_top1, z_top2) - max(z_bottom1, z_bottom2))

    # Compute 3D IoU
    inter_volume = inter_2d * inter_z
    vol1 = l1 * w1 * h1
    vol2 = l2 * w2 * h2
    union_volume = vol1 + vol2 - inter_volume

    return inter_volume / union_volume if union_volume > 0 else 0.0

def rotz(t):
    ''' Rotation about the y-axis. '''
    c = np.cos(t)
    s = np.sin(t)
    return np.array([[c,  -s,  0],
                     [s,  c,  0],
                     [0, 0,  1]])

def convert_3dbox_to_8corner(bbox3d_input):
    ''' Takes an object and a projection matrix (P) and projects the 3d
        bounding box into the image plane.
        Returns:
            corners_2d: (8,2) array in left image coord.
            corners_3d: (8,3) array in in rect camera coord.
    '''
    # compute rotational matrix around yaw axis
    bbox3d = copy.copy(bbox3d_input)

    R = rotz(bbox3d[3])

    # 3d bounding box dimensions
    l = bbox3d[4]
    w = bbox3d[5]
    h = bbox3d[6]

    # 3d bounding box corners
    x_corners = [l/2,l/2,-l/2,-l/2,l/2,l/2,-l/2,-l/2];
    y_corners = [h/2,h/2,h/2,h/2,-h/2,-h/2,-h/2,-h/2];
    z_corners = [w/2,-w/2,-w/2,w/2,w/2,-w/2,-w/2,w/2];

    # rotate and translate 3d bounding box
    corners_3d = np.dot(R, np.vstack([x_corners,y_corners,z_corners]))
    corners_3d[0,:] = corners_3d[0,:] + bbox3d[0]
    corners_3d[1,:] = corners_3d[1,:] + bbox3d[1]
    corners_3d[2,:] = corners_3d[2,:] + bbox3d[2]

    return np.transpose(corners_3d)

def generate_ID():
    """
    Generate tracking IDs to identify objects
    """
    return str(uuid.uuid1())

class KalmanBoxTracker(object):
  """
  This class represents the internel state of individual tracked objects observed as bbox.
  """
  count = 0
  def __init__(self, bbox3D, label, category):
    """
    Initialises a tracker using initial bounding box.
    """
    #define constant velocity model
    self.kf = KalmanFilter(dim_x=10, dim_z=7)
    self.kf.F = np.array([[1,0,0,0,0,0,0,1,0,0],      # state transition matrix
                          [0,1,0,0,0,0,0,0,1,0],
                          [0,0,1,0,0,0,0,0,0,1],
                          [0,0,0,1,0,0,0,0,0,0],
                          [0,0,0,0,1,0,0,0,0,0],
                          [0,0,0,0,0,1,0,0,0,0],
                          [0,0,0,0,0,0,1,0,0,0],
                          [0,0,0,0,0,0,0,1,0,0],
                          [0,0,0,0,0,0,0,0,1,0],
                          [0,0,0,0,0,0,0,0,0,1]])

    self.kf.H = np.array([[1,0,0,0,0,0,0,0,0,0],      # measurement function,
                          [0,1,0,0,0,0,0,0,0,0],
                          [0,0,1,0,0,0,0,0,0,0],
                          [0,0,0,1,0,0,0,0,0,0],
                          [0,0,0,0,1,0,0,0,0,0],
                          [0,0,0,0,0,1,0,0,0,0],
                          [0,0,0,0,0,0,1,0,0,0]])

    self.kf.P[7:,7:] *= 1000. # state covariance matrix, give high uncertainty to the unobservable initial velocities, covariance matrix
    self.kf.P *= 10.
    self.kf.Q[7:,7:] *= 0.01
    self.kf.x[:7] = bbox3D.reshape((7, 1))
    self.label = label
    self.category = category  # Added category attribute


    self.time_since_update = 0

    self.id = KalmanBoxTracker.count
    KalmanBoxTracker.count += 1
    self.unique_id = generate_ID()
    self.history = []
    self.hits = 1           # number of total hits including the first detection
    self.hit_streak = 1     # number of continuing hit considering the first detection
    self.first_continuing_hit = 1
    self.still_first = True
    self.age = 0
    self.tracked = True

  def update(self, bbox3D: list) -> None:
    """
    Updates the state vector with observed bbox.

    Parameters
    ----------
    bbox3d: list
      list representing a 3d bbox [x,y,z,yaw,l,w,h]

    Returns
    -------
    None
    """
    self.time_since_update = 0
    self.history = []
    self.hits += 1
    self.hit_streak += 1          # number of continuing hit
    if self.still_first:
      self.first_continuing_hit += 1      # number of continuing hit in the fist time

    ######################### orientation correction
    if self.kf.x[3] >= np.pi: self.kf.x[3] -= np.pi * 2    # make the theta still in the range
    if self.kf.x[3] < -np.pi: self.kf.x[3] += np.pi * 2

    new_theta = bbox3D[3]
    if new_theta >= np.pi: new_theta -= np.pi * 2    # make the theta still in the range
    if new_theta < -np.pi: new_theta += np.pi * 2
    bbox3D[3] = new_theta

    predicted_theta = self.kf.x[3]
    if abs(new_theta - predicted_theta) > np.pi / 2.0 and abs(new_theta - predicted_theta) < np.pi * 3 / 2.0:     # if the angle of two theta is not acute angle
      self.kf.x[3] += np.pi
      if self.kf.x[3] > np.pi: self.kf.x[3] -= np.pi * 2    # make the theta still in the range
      if self.kf.x[3] < -np.pi: self.kf.x[3] += np.pi * 2

    # now the angle is acute: < 90 or > 270, convert the case of > 270 to < 90
    if abs(new_theta - self.kf.x[3]) >= np.pi * 3 / 2.0:
      if new_theta > 0: self.kf.x[3] += np.pi * 2
      else: self.kf.x[3] -= np.pi * 2

    #########################

    self.kf.update(bbox3D)

    if self.kf.x[3] >= np.pi: self.kf.x[3] -= np.pi * 2    # make the theta still in the range
    if self.kf.x[3] < -np.pi: self.kf.x[3] += np.pi * 2

  def predict(self):
    """
    Advances the state vector and returns the predicted bounding box estimate.
    """
    self.kf.predict()
    if self.kf.x[3] >= np.pi: self.kf.x[3] -= np.pi * 2
    if self.kf.x[3] < -np.pi: self.kf.x[3] += np.pi * 2

    self.age += 1
    if(self.time_since_update>0):
      self.hit_streak = 0
      self.still_first = False
    self.time_since_update += 1
    self.history.append(self.kf.x)
    return self.history[-1]

  def get_state(self):
    """
    Returns the current bounding box estimate.
    """
    return self.kf.x


def associate_detections_to_trackers(detections: np.ndarray, trackers: np.ndarray, config: dict) -> tuple:
  """
  Assigns detections to tracked object based on l2 norm

  Parameters
  ----------
  detections: np.ndarray
    N x 8 x 3 numpy array of 8 corners representing a detection
  trackers: np.ndarray
    M x 8 x 3 numpy array of 8 corners representing a tracker prediction
  config: dict
    Configuration dictionary containing l2_thresh and other parameters

  Returns
  -------
  tuple
  matches: list
    list of matched intput detection and track list indices
  unmatched_detections: np.ndarray
    numpy array with indices (of input detections array) of unmatched detections
  unmatched tracks: np.ndarray
    numpy array with indices (of input track array) of unmatched tracks
  """
  l2_threshold = config['l2_thresh']

  # If tracker array is empty then no matching can happen
  if(len(trackers)==0):
    return np.empty((0,2),dtype=int), np.arange(len(detections)), np.empty((0,5),dtype=int)

  # If no detections, return empty matches
  if(len(detections)==0):
    return np.empty((0,2),dtype=int), np.empty((0,),dtype=int), np.arange(len(trackers))

  # Initialize cost matrix
  cost_matrix = np.zeros((len(detections),len(trackers)),dtype=np.float32)

  for d,det in enumerate(detections):
    for t,trk in enumerate(trackers):
      # Uncomment iou to use iou metric, adjust config['l2_thresh'] according to metric being used
      cost_matrix[d,t] = np.linalg.norm(np.mean(np.array(det), axis=0) - np.mean(np.array(trk), axis=0))# iou3d(det,trk)[0]# det: 8 x 3, trk: 8 x 3

  valid_rows = [i for i in range(len(detections)) if cost_matrix[i].min() <= l2_threshold]

  # If no valid rows, all detections are unmatched
  if len(valid_rows) == 0:
    return np.empty((0,2),dtype=int), np.arange(len(detections)), np.arange(len(trackers))

  reduced_cost = cost_matrix[valid_rows]
  row_inds, col_inds = linear_sum_assignment(reduced_cost)      # hungarian algorithm

  # Get matched indices between detections and tracker predictions
  matched_indices = np.array([[valid_rows[i],j] for i,j in zip(row_inds, col_inds)])

  # Get unmatched detections
  unmatched_detections = []
  for d in range(len(detections)):
    if len(matched_indices) == 0 or d not in matched_indices[:,0]:
      unmatched_detections.append(d)

  # Get unmatched trackers
  unmatched_trackers = []
  for t in range(len(trackers)):
    if len(matched_indices) == 0 or t not in matched_indices[:,1]:
      unmatched_trackers.append(t)

  #filter out matches with don't satisfy constraints, currently tuned for l2 distance
  matches = []
  for m in matched_indices:
    if(cost_matrix[m[0],m[1]]>l2_threshold):
      unmatched_detections.append(m[0])
      unmatched_trackers.append(m[1])
    else:
      matches.append(m.reshape(1,2))

  if(len(matches)==0):
    matches = np.empty((0,2),dtype=int)
  else:
    matches = np.concatenate(matches,axis=0)

  return matches, np.array(unmatched_detections), np.array(unmatched_trackers)


class AB3DMOT(object):
  """
  Multi Object Tracker Class
  Maintains tracks across multiple frames for a specific category
  """
  def __init__(self, category, config):      # max age will preserve the bbox does not appear no more than 2 frames, interpolate the detection
    self.category = category  # Track category (vehicle, human, large_vehicle)
    self.config = config
    self.max_age_far = config['mal']
    self.max_age_near = config['mah']
    self.max_age = self.max_age_near
    self.keep_age = config['keep_age']
    self.min_hits = config['min_hits']
    self.trackers = []
    self.frame_count = 0
    self.static_locs, self.static_ids = [],[]
    self.track_status = {}

  def update(self, detections: np.ndarray, center: tuple=None, labels=None) -> np.ndarray:
    """
    Matches active tracks with the detections in new frame.
    Creates new tracks for unmatched detections if they appear in more than self.min_hits consecutive frames.
    Removes tracks which have not been updated in consecutive self.max_age frames.
    Updates tracks which match with detections based on iou overlap/l2 distance
    For the first self.min_hits frames all the detections are considered as tracks

    Parameters
    ----------
    detections: np.ndarray
      Numpy array of detections in the format [[x,y,z,theta,l,w,h],[x,y,z,theta,l,w,h],...]

    Returns
    -------
    frame_tracks: list
      List of detections that belong to new/existing tracks along with track id and other meta information about the detecions
      Each element is a tuple consisting these items:
      matched_detection: 3d bbox of detection that matched with a tracker
      track.id+1: Integer Id of the track, 1 added to avoid starting from zero
      track.unique_id: uuid of the track
      track.tracked: Whether the track had a match in this frame
      id_to_predictions[track.id+1]: Tracker prediction for this frame that was used to find a macthing detection

    """
    #Initialisations
    tracked = []
    self.frame_count += 1
    id_to_predictions = {}
    active_tracks = np.zeros((len(self.trackers),7))         # N x 7 , #get predicted locations from existing trackers.
    to_del = []
    frame_tracks = []

    # For the active tracks extract the 3d bbox from the predicted state
    # Store tracks to delete with nan in prediction
    for i,track in enumerate(active_tracks):
      position = self.trackers[i].predict().reshape((-1, 1))
      track[:] = np.squeeze(position)[:7]
      id_to_predictions[self.trackers[i].id+1] = list(np.squeeze(track))
      if(np.any(np.isnan(position))):
        to_del.append(i)

    # np.squeeze
    # active_trks = np.ma.compress_rows(np.ma.masked_invalid(active_tracks))

    # Delete tracks with nan in predicted state
    for i in reversed(to_del):
      self.trackers.pop(i)

    # Convert detections from 3d bbox to 8x3 corners format
    detections_8corner = [convert_3dbox_to_8corner(detection) for detection in detections]

    # In case of no detections for this frame, use empty list
    if len(detections_8corner) > 0: dets_8corner = np.stack(detections_8corner, axis=0)
    else: dets_8corner = []

    # Convert track 3d bbox to 8x3 corner format
    tracks_8corner = [convert_3dbox_to_8corner(track) for track in active_tracks]


    if len(tracks_8corner) > 0:
      tracks_8corner = np.stack(tracks_8corner, axis=0)

    # Match active tracks with detections based on iou overlap/l2 distance between predicted state and actual detection
    matched, unmatched_detections, unmatched_tracks = associate_detections_to_trackers(dets_8corner,
                                                                                       tracks_8corner,
                                                                                       self.config)


    # Update matched trackers with assigned detections
    for i,track in enumerate(self.trackers):
      if i not in unmatched_tracks:
        matched_detection = matched[np.where(matched[:,1]==i)[0],0]     # a list of index
        track.update(detections[matched_detection,:][0])
        track.tracked = True
      else:
        track.tracked = False

    # Create and initialise new trackers for unmatched detections
    # Also store state predictions in id_to_predictions dictionary
    for i in unmatched_detections:
      track = KalmanBoxTracker(detections[i,:], labels[i], self.category)
      self.trackers.append(track)
      id_to_predictions[track.id+1] = list(np.squeeze(detections[i,:]))


    # For each track verify if it satisfies the constraints of min hits and max_age
    # For all the tracks satisdying the constraints, return the matched 3d bbox in this frame
    i = len(self.trackers)
    for track in reversed(self.trackers):
      tracker_state = track.kf.x
      bbox_3d = np.squeeze(tracker_state)[:7]

      if((track.time_since_update < self.max_age) and (track.hits >= self.min_hits or self.frame_count <= self.min_hits)):
        frame_tracks.append(  ( bbox_3d, track.id+1, track.unique_id, track.tracked, id_to_predictions[track.id+1], track.label, track.category  ) ) # +1 as MOT benchmark requires positive
      i -= 1

      if track.time_since_update >= self.keep_age:
        self.trackers.pop(i)

    if(len(frame_tracks)>0):
      return frame_tracks

    # If no tracks present in this frame, return empty list
    return np.empty((0,16))


class MultiCategoryTracker(object):
    """
    Multi-category tracker that manages separate trackers for different object categories
    """
    def __init__(self, config):
        self.config = config
        self.vehicle_tracker = AB3DMOT("vehicle", config)  # Combined vehicle and large_vehicle
        self.human_tracker = AB3DMOT("human", config)

    def update(self, detections, labels, parent_labels):
        """
        Update all category trackers with their respective detections
        """
        all_tracks = []

        # Separate detections by category
        vehicle_mask = np.array([label == "vehicle" for label in parent_labels])
        human_mask = np.array([label == "human" for label in parent_labels])

        # Track vehicles (including large vehicles)
        if np.any(vehicle_mask):
            vehicle_detections = detections[vehicle_mask]
            vehicle_labels = labels[vehicle_mask]
            vehicle_tracks = self.vehicle_tracker.update(vehicle_detections, labels=vehicle_labels)
            all_tracks.extend(vehicle_tracks)

        # Track humans
        if np.any(human_mask):
            human_detections = detections[human_mask]
            human_labels = labels[human_mask]
            human_tracks = self.human_tracker.update(human_detections, labels=human_labels)
            all_tracks.extend(human_tracks)

        return all_tracks


def quaternion_to_yaw(quaternion: list) -> float:
    """
    Convert a quaternion to yaw (rotation around z-axis) in radians using SciPy.

    Parameters
    ----------
    quaternion: list or np.array
      Quaternion in [x, y, z, w] format

    Returns
    -------
    float
      Yaw angle in radians
    """
    # Create a Rotation object from the quaternion
    # Note: SciPy uses [w, x, y, z] format, so we need to reorder
    rot = R.from_quat([quaternion[3], quaternion[0], quaternion[1], quaternion[2]])

    # Get Euler angles in the standard aerospace sequence (z-y'-x'')
    # This returns [roll, pitch, yaw] in radians
    euler_angles = rot.as_euler('xyz')

    # Return just the yaw (third angle)
    return euler_angles[2]

def get_gt_boxes(gt_data: dict) -> list:
  """
  Function to parse vehicle 3d bbox from the annotations

  Parameters
  ----------
  gt_data: dict
    Dictionary containing ground truth annotations

  Returns
  -------
  box_list: list
    List of 3d bboxes for vehicle category in the annotations
  """
  box_list = []
  for obj in gt_data["annotations"]:
      if obj["object_type"] == "cuboid" and obj["label"] == "Vehicle":
          x,y,z = obj["position"]
          l,w,h = obj["dimension"]
          yaw = quaternion_to_yaw(obj["orientation"])
          box_list.append([x,y,z,l,w,h,yaw])

  return box_list

def nms_3d(boxes, scores, iou_threshold):
    indices = np.argsort(scores)[::-1]
    keep = []

    while len(indices) > 0:
        current = indices[0]
        keep.append(current)

        suppressed = [0]  # always keep current
        for i in range(1, len(indices)):
            iou = compute_3d_iou(boxes[current], boxes[indices[i]])
            if iou > iou_threshold:
                suppressed.append(i)

        indices = np.delete(indices, suppressed)

    return keep

def preprocess(detections, scores, labels, config):
    # Define parent category mappings
    parent_to_sub = {
        "vehicle": [1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],  # Regular_vehicle + all large vehicle types
        "human": [2, 3, 4, 5],  # Pedestrian, Bicyclist, Motorcyclist, Wheeled_rider
    }

    # Get confidence thresholds for each parent category
    vehicle_thresh = config['min_score_threshold']['vehicle']
    human_thresh = config['min_score_threshold']['pedestrian']

    # Create masks for each parent category with their respective thresholds
    vehicle_mask = (scores > vehicle_thresh) & ([label in parent_to_sub["vehicle"] for label in labels])
    human_mask = (scores > human_thresh) & ([label in parent_to_sub["human"] for label in labels])

    # Combine masks to get final filtering mask
    mask = vehicle_mask | human_mask

    detections = detections[mask]
    scores = scores[mask]
    labels = labels[mask]

    # Apply class agnostic nms
    keep_indices = nms_3d(detections, scores, iou_threshold=0)
    detections = detections[keep_indices]
    scores = scores[keep_indices]
    labels = labels[keep_indices]

    # Club sub_categories into parent category
    parent_labels = []
    for label in labels:
        if label in parent_to_sub["vehicle"]:
            parent_labels.append("vehicle")
        elif label in parent_to_sub["human"]:
            parent_labels.append("human")

    return detections, scores, labels, parent_labels

def run_tracker(detections_folder: str, save_path: str, ego_data_path=None, config=None, config_path=None, data_path=None) -> None:
  """
  Runs Extended Kalman Filter on a sequence of input lidar frames and stores the tracking results in a json file for each frame

  Parameters
  ----------
  detections_folder: str
    Path to detections folder
    Detections are stored in json file in the format:
    [
      {
      "pred_boxes": [[x,y,z,l,w,h,yaw], ...],
      "pred_scores": [0.9, ...],
      "pred_labels": [1, ....] # class labels
      }
    ]

  save_path: str
    Path where tracking results will be dumped

  ego_data_path: str, optional
    Path to ego vehicle data

  config: dict, optional
    Configuration dictionary with tracking parameters

  config_path: str, optional
    Path to JSON configuration file

  Returns
  -------
  None

  Json format for saved tracks is like this:
  [
      {
      "center": {
          "x": x,
          "y": y,
          "z": z
      },
      "length": l,
      "width": w,
      "height": h,
      "yaw": yaw,
      "tracked": true/false,
      "prediction": [x,y,z,l,w,h,yaw],
      "label_class": "VEHICLE",
      "track_label_uuid": "093d1889-05b9-11f0-8b60-abb0e630eb3c",
      "id_cnt": 16
  }, ...
  ]
  """
  # Load configuration
  config = load_config(config_path, config)

  # sort the files to ensure tracking happens in proper order
  seq_file_list = sorted(os.listdir(detections_folder), key=lambda x: x.split('_')[-1])
  # Insert key frame in the middle
  middle = len(seq_file_list) // 2
  final_list = []


  seq_file_list = sorted(os.listdir(detections_folder), key=lambda x: x.split('_')[-1])
  fc1_file_list = sorted([f for f in os.listdir(data_path) if 'FC1' in f ],
                        key=lambda x: x.split('_')[-1].split('.')[0])
  file_mappings_path = os.path.join(os.path.dirname(os.path.abspath(detections_folder)), 'file_mappings.json')
  data=json.load(open(file_mappings_path))
  for i in fc1_file_list:
      for j in data :
          if j['fc1_basename'] == i:
              final_list.append(os.path.basename(j['RefLidar']).replace('.pcd', '.json'))

  # For fps computation
  total_time = 0.0
  total_frames = 0

  save_dir = os.path.join(save_path)
  os.makedirs(save_path, exist_ok=True)

  # Initialize Multi Category Tracker with config
  mot_tracker = MultiCategoryTracker(config)

  # Loop over each lidar frame in sequence
  for frame, seq_file in enumerate(final_list):
    seq_name = seq_file[:-5]


    # Load detection results for the frame from json file
    with open(os.path.join(detections_folder, seq_file), "r") as f:
      data = json.load(f)

    # Use this to parse detections from model predictions using predict_openpcde.py script
    detections = np.array(data[0]["pred_boxes"])
    scores  = np.array(data[0]["pred_scores"])
    labels = np.array(data[0]["pred_labels"])

    lidar_to_world = None
    if ego_data_path is not None:
      with open(os.path.join(ego_data_path, seq_file), "r") as f:
        ego_data = json.load(f)

      lidar_to_world = np.array(ego_data["ego"]["transformationMatrix"]).reshape((4,4))

      for i, detection in enumerate(detections):
        lidar_pos = np.array([*detection[:3],1])
        world_pos = np.dot(lidar_to_world, lidar_pos)[:3]
        detections[i][:3] = world_pos

        yaw_lidar = detection[6]
        R_yaw_lidar = np.array([
            [np.cos(yaw_lidar), -np.sin(yaw_lidar), 0],
            [np.sin(yaw_lidar),  np.cos(yaw_lidar), 0],
            [0,               0,               1]
        ])
        R_world = lidar_to_world[:3, :3]
        R_yaw_world = R_world @ R_yaw_lidar

        # Extract yaw from rotation matrix (assuming Z-axis up)
        yaw_w = np.arctan2(R_yaw_world[1, 0], R_yaw_world[0, 0])
        detections[i][6] = yaw_w


    logger.info(f"Processing {seq_name}", **logger_params )
    detections, scores, labels, parent_labels = preprocess(detections, scores, labels, config)
    class_names = ['Vehicle', 'Pedestrian', 'Human', 'Human', 'Human',
            'Bollard', 'Construction_cone', 'Sign', 'Construction_barrel', 'Stop_sign', 'Mobile_pedestrian_crossing_sign',
            'LargeVehicle', 'Bus', 'Truck', 'Truck', 'Vehicle', 'Truck', 'Bus', 'Bus',
            'Vehicle', 'Bicycle', 'Motorbike', 'Wheeled_device', 'Wheelchair', 'Stroller', 'Dog']
    # convert detectins in this order: [x,y,z,yaw,l,w,h]
    detections = detections[:,[0,1,2,6,3,4,5]]


    # Save all the tracks identified in this frame in this list
    track_list = []
    total_frames += 1

    # Get active tracks based on the detections in this frame using multi-category tracker
    start_time = time.time()
    trackers = mot_tracker.update(detections, labels=labels, parent_labels=parent_labels)
    frame_time = time.time() - start_time
    total_time += frame_time
    z_min, z_max = [-10,10]

    # Process tracks from all categories
    for (bbox_3d, track_id_cnt, unique_id, tracked, prediction, label, category) in trackers:
      x,y,z,yaw,l,w,h = bbox_3d


      if ego_data_path is not None:
        world_to_lidar = np.linalg.inv(lidar_to_world)
        world_pos_homogeneous = np.array([x,y,z,1])
        x,y,z = np.dot(world_to_lidar, world_pos_homogeneous)[:3]

        rot_yaw_world = np.array([
          [np.cos(yaw), -np.sin(yaw), 0],
          [np.sin(yaw),  np.cos(yaw), 0],
          [0,               0,               1]
          ])

        rot_world_to_lidar = world_to_lidar[:3, :3]
        rot_yaw_lidar = rot_world_to_lidar @ rot_yaw_world
        yaw_lidar = np.arctan2(rot_yaw_lidar[1, 0], rot_yaw_lidar[0, 0])
        yaw = yaw_lidar

      track = {
            "id": "",
            "identity": track_id_cnt,
            "classId": f"{label}",
            "class": class_names[label-1],
            "category": category,  # Add category information

            "geometry": {
                "id": "",
                "position": {
                    "x": x,
                    "y": y,
                    "z": z,
                },
                "rotation": {
                    "x": 0,
                    "y": 0,
                    "z": yaw,
                },
                "boxSize": {
                    "x": l,
                    "y": w,
                    "z": h,
                },
                "clipTask": 1,
                "referenceSourceIds": [],
            },
            "taxonomy_attribute":
                {
                    "isAttachedTo2D": "1",
            },
            "verticalLimit": [],
            "groundClippingMode": 0,
            "isGeometryKeyFrame": True,
            "isAttributeKeyFrame": False,
            "isFirstTimeAttribute": False,
            "updated_on": '',
            "object_type": "cuboid",
            "isUpdated": False,
            "node_wise_batch_details_id": "",
            "uniqueToken": "",
            "isLocked": False,
            "isVisible": True,
        }
      track['id'] = str(uuid.uuid1())
      track["geometry"]["id"] = str(uuid.uuid1())
      track["verticalLimit"].append(z_min)
      track["verticalLimit"].append(z_max)

      track_list.append(track)

    save_path_file = os.path.join(save_dir,f"tracked_object_labels_{seq_name}.json" )

    # Dump all the tracks for this frame in a json file
    with open (save_path_file, "w") as outfile:
      json.dump(track_list, outfile, indent=4)

  logger.info(f"Total Tracking took: {total_time} for {total_frames} frames or {total_frames/total_time} FPS", **logger_params)


def create_sample_config():
    """
    Create a sample configuration file for reference
    """
    sample_config = {
        "dthresh": 0.1,
        "mal": 2,
        "mah": 2,
        "min_hits": 3,
        "l2_thresh": 5,
        "cat": "VEHICLE",
        "set": "val",
        "keep_age": 3,
        "svel": 0.3,
        "sdis": 2
    }

    with open("tracker_config.json", "w") as f:
        json.dump(sample_config, f, indent=4)

    print("Sample configuration file 'tracker_config.json' created.")


if __name__ == '__main__':
  """
  Calls function to run multi object tracking on a seqence of lidar frames
  """
  # Parse command line arguments for backwards compatibility
  parser = argparse.ArgumentParser(description="arg parser")
  parser.add_argument('--config', type=str, help='Path to configuration JSON file')
  parser.add_argument('--create-config', action='store_true', help='Create sample configuration file')

  # Keep original arguments as fallbacks
  parser.add_argument('--dthresh', type=float, default=0.1, help='detection threshold')
  parser.add_argument('--mal', type=int, default=2, help='max age lower value for termination of tracks')
  parser.add_argument('--mah', type=int, default=2, help='max age higher value for termination of tracks')
  parser.add_argument('--min_hits', type=int, default=3, help='min hits for initializing new tracks')
  parser.add_argument('--l2_thresh', type=float, default=5, help='l2 distance for matching tracks to detections')
  parser.add_argument('--cat', type=str, default="VEHICLE", help='category vehicle/pedestrian')
  parser.add_argument('--set', type=str, default="val", help='val/train/test')
  parser.add_argument('--keep_age', type=int, default=3, help='age for which to keep the track')
  parser.add_argument('--svel', type=float, default=0.3, help='velocity threshold')
  parser.add_argument('--sdis', type=float, default=2, help='distance threshold')

  args, unknown = parser.parse_known_args()

  if args.create_config:
      create_sample_config()
      exit(0)

  # Load configuration
  if args.config:
      config = load_config(config_path=args.config)
  else:
      # Use command line arguments as config for backwards compatibility
      config = {
          'dthresh': args.dthresh,
          'mal': args.mal,
          'mah': args.mah,
          'min_hits': args.min_hits,
          'l2_thresh': args.l2_thresh,
          'cat': args.cat,
          'set': args.set,
          'keep_age': args.keep_age,
          'svel': args.svel,
          'sdis': args.sdis
      }

  # Input paths
  save_path = os.path.abspath('/home/<USER>/CV/ml-b_rfi/outputs/3d_tracking_output')
  detections_folder = "/home/<USER>/CV/ml-b_rfi/outputs/3d_detection_output"

  run_tracker(detections_folder, save_path, config=config)