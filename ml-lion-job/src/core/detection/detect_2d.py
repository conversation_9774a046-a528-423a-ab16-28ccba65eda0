# This script is used to detect objects in an image using Faster R-CNN with SAHI slicing technique.
import os
import cv2
import glob
import torch
from torchvision import models
from torchvision.ops import nms
from PIL import Image
import numpy as np
from sahi.models.torchvision import TorchVisionDetectionModel
from sahi import AutoDetectionModel
from sahi.predict import get_sliced_prediction
from sahi.utils.cv import read_image
from sahi.postprocess.combine import GreedyNMMPostprocess
import json
from .conversion_utils import to_serializable
from tqdm import tqdm

# Import logger utilities
from utils import logger
from utils import logger_params


def compute_intersection_area(box1, box2):
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    return max(0, x2 - x1) * max(0, y2 - y1)

def box_area(box):
    return max(0, box[2] - box[0]) * max(0, box[3] - box[1])

def boxes_with_high_self_intersection_ratio(boxes,
                                            labels_nms,
                                            scores,
                                            inter_threshold=0.5,
                                            min_side_threshold = 30,
                                            max_side_threshold = 1e3,
                                            filter_boundary_boxes = False,
                                            camera = None,
                                            boundary_config = None):
    """
    Filter boxes based on intersection ratios and other criteria.
    
    Args:
        boundary_config: Dictionary with boundary filtering parameters
                        e.g., {'boundary_x_max': 1486, 'boundary_x_min': 50, 'boundary_y_min_height': 120}
    """
    boxes = np.array(boxes)
    result = []
    ignore = []
    
    if filter_boundary_boxes and boundary_config:
        boundary_x_max = boundary_config.get('boundary_x_max', 1486)
        boundary_x_min = boundary_config.get('boundary_x_min', 50)
        boundary_y_min_height = boundary_config.get('boundary_y_min_height', 120)
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes[i]
            if (x2 > boundary_x_max or x1 < boundary_x_min) and (y2 - y1) > boundary_y_min_height:
                ignore.append(i)
    elif filter_boundary_boxes:
        # Fallback to hardcoded values if no config provided
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes[i]
            if (x2 > 1486 or x1 < 50) and (y2 - y1) > 120:
                ignore.append(i)
                
    input_side_threshold = min_side_threshold
    for i in range(len(boxes)):
        area_i = box_area(boxes[i])
        label_i = labels_nms[i]
        if label_i == 0:
            min_side_threshold = 5
        else:
            min_side_threshold = input_side_threshold
        if area_i == 0 or i in ignore:
            continue
        max_ratio = 0
        for j in range(len(boxes)):
            if i == j or j in ignore:
                continue
            inter_area = compute_intersection_area(boxes[i], boxes[j])
            area_j = box_area(boxes[j])
            ratio_i = inter_area / area_i
            ratio_j = inter_area / area_j

            if ratio_i > inter_threshold and ratio_j > inter_threshold:
                if scores[i] <= scores[j]:
                    max_ratio = 1
            else:
                max_ratio = max(ratio_i, max_ratio)

        if  ( (max_ratio < inter_threshold) and
            min(np.abs(boxes[i][0] - boxes[i][2]), np.abs(boxes[i][1] - boxes[i][3])) > min_side_threshold and
            max(np.abs(boxes[i][0] - boxes[i][2]), np.abs(boxes[i][1] - boxes[i][3])) < max_side_threshold):
            result.append(i)

    return result


def detect_objects_with_sahi(image_paths,
                             config=None,
                             sahi_model=None,
                             save_json_file=None):
    """
    Detect objects in images using SAHI slicing technique.

    Args:
        image_paths (list): List of paths to input images
        config (dict): Configuration dictionary for 2D detection settings
        sahi_model: Pre-initialized SAHI model (optional)
        save_json_file (str): Path to save output JSON file

    Returns:
        list: Detection results for all images
    """
    config = config or {}
    
    # Get YOLO labels from config or use default
    YOLO_LABELS = config.get('yolo_labels', {
        "0": 'Pedestrian', "1": 'Bicycle', "2": 'Vehicle', "3": 'Motorbike',
        "4": 'Airplane', "5": 'Bus', "6": 'Train', "7": 'Truck', "8": 'Boat'
    })
    
    # Convert string keys to integers for label mapping
    YOLO_LABELS = {int(k): v for k, v in YOLO_LABELS.items()}

    if sahi_model is None:
        # Initialize model with config parameters
        model_type = config.get('model_type', 'yolov8')
        model_path = config.get('model_path', 'yolov8x.pt')
        device = config.get('device', 'cuda')
        base_confidence = config.get('base_confidence_threshold', 0.1)
        
        sahi_model = AutoDetectionModel.from_pretrained(
            model_type=model_type,
            model_path=model_path,
            confidence_threshold=base_confidence,
            device=device,
        )

    # Get default values from config
    overlap_height_ratio = config.get('overlap_height_ratio', 0.2)
    overlap_width_ratio = config.get('overlap_width_ratio', 0.2)
    filter_classIDs = config.get('filter_class_ids', None)
    show_results = config.get('show_results', True)
    print_info = config.get('print_info', False)
    camera_settings = config.get('camera_specific_settings', {})

    info_list = []
    for image_path in tqdm(image_paths, desc="Processing images"):
        img = read_image(image_path)

        # Determine camera type and get settings
        camera_type = None
        for cam_name in camera_settings.keys():
            if cam_name in image_path:
                camera_type = cam_name
                break
        
        # Get camera-specific settings or use defaults
        if camera_type and camera_type in camera_settings:
            cam_config = camera_settings[camera_type]
            sahi_model.confidence_threshold = cam_config.get('confidence_threshold', 0.4)
            slice_height = cam_config.get('slice_height', config.get('default_slice_height', 640))
            slice_width = cam_config.get('slice_width', config.get('default_slice_width', 640))
            postprocess_match_threshold = cam_config.get('postprocess_match_threshold', 0.5)
        else:
            # Default settings
            sahi_model.confidence_threshold = 0.4
            slice_height = config.get('default_slice_height', 640)
            slice_width = config.get('default_slice_width', 640)
            postprocess_match_threshold = 0.5

        # Perform SAHI sliced prediction
        result = get_sliced_prediction(
            img,
            sahi_model,
            slice_height=slice_height,
            slice_width=slice_width,
            overlap_height_ratio=overlap_height_ratio,
            overlap_width_ratio=overlap_width_ratio,
            postprocess_match_threshold=postprocess_match_threshold,
            verbose=0
        )

        # Extract detection results using the bbox attribute
        preds = result.object_prediction_list
        bboxes_nms = np.array([det.bbox.to_xyxy() for det in result.object_prediction_list])
        scores_nms = np.array([det.score.value for det in result.object_prediction_list])
        labels_nms = np.array([det.category.id for det in result.object_prediction_list])

        # Check if length of bboxes is 0. then add empty list to info_list
        if len(bboxes_nms) == 0:
            info_ = {
                'image_path': image_path,
                'bboxes': [],
                'scores': [],
                'labels': []
            }
            info_list.append(info_)
            continue

        if print_info:
            # Log the filtered detection results
            logger.info('[INFO]: After NMS Filtering:', **logger_params)
            logger.info(f"Filtered Bounding Boxes: {bboxes_nms}", **logger_params)
            logger.info(f"Filtered Scores: {scores_nms}", **logger_params)
            logger.info(f"Filtered Labels: {labels_nms}", **logger_params)

        # Filter detections based on filter_classes
        if filter_classIDs is not None:
            mask = np.isin(labels_nms, filter_classIDs)
            bboxes_nms = bboxes_nms[mask]
            scores_nms = scores_nms[mask]
            labels_nms = labels_nms[mask]
            preds = [preds[mask_i] for mask_i in range(len(mask)) if mask[mask_i]]

            if print_info:
                # Log the filtered detection results
                logger.info('[INFO]: After Class Filtering:', **logger_params)
                logger.info(f"Filtered Bounding Boxes: {bboxes_nms}", **logger_params)
                logger.info(f"Filtered Scores: {scores_nms}", **logger_params)
                logger.info(f"Filtered Labels: {labels_nms}", **logger_params)

        # Additional filtering: only keep labels that exist in YOLO_LABELS
        valid_label_mask = np.isin(labels_nms, list(YOLO_LABELS.keys()))
        bboxes_nms = bboxes_nms[valid_label_mask]
        scores_nms = scores_nms[valid_label_mask]
        labels_nms = labels_nms[valid_label_mask]
        preds = [preds[i] for i in range(len(valid_label_mask)) if valid_label_mask[i]]

        if print_info and len(labels_nms) > 0:
            logger.info('[INFO]: After YOLO_LABELS Filtering:', **logger_params)
            logger.info(f"Valid Labels: {labels_nms}", **logger_params)

        filter_inds = list(range(len(bboxes_nms)))
        
        # Apply camera-specific filtering using config
        if camera_type and camera_type in camera_settings:
            cam_config = camera_settings[camera_type]
            filter_inds = boxes_with_high_self_intersection_ratio(
                bboxes_nms,
                labels_nms,
                scores_nms,
                inter_threshold=cam_config.get('intersection_threshold', 0.8),
                min_side_threshold=cam_config.get('min_side_threshold', 30),
                max_side_threshold=cam_config.get('max_side_threshold', 1000),
                filter_boundary_boxes=cam_config.get('filter_boundary_boxes', False),
                camera=camera_type,
                boundary_config=cam_config  # Pass the entire camera config for boundary settings
            )
        else:
            # Default filtering
            filter_inds = boxes_with_high_self_intersection_ratio(
                bboxes_nms,
                labels_nms,
                scores_nms,
                inter_threshold=0.8,
                min_side_threshold=30,
                max_side_threshold=1000,
                filter_boundary_boxes=False,
                camera=None
            )

        bboxes_nms = bboxes_nms[filter_inds]
        scores_nms = scores_nms[filter_inds]
        labels_nms = labels_nms[filter_inds]
        preds = [preds[filter_i] for filter_i in filter_inds]

        # Apply Greedy NMM postprocessing for specific cameras
        if camera_type in ['TV_left', 'TV_right']:
            greedy_config = config.get('greedy_nmm_postprocess', {})
            gnmm = GreedyNMMPostprocess(
                match_metric=greedy_config.get('match_metric', 'IOS'),
                class_agnostic=greedy_config.get('class_agnostic', False),
            )
            final_preds = gnmm(preds)
            bboxes_nms = np.array([det.bbox.to_xyxy() for det in final_preds])
            scores_nms = np.array([det.score.value for det in final_preds])
            labels_nms = np.array([det.category.id for det in final_preds])


        # Convert label IDs to label names (now all labels should be valid)
        labels_nms = [YOLO_LABELS[label_i] for label_i in labels_nms]


        info_ = {
            'image_path': image_path,
            'bboxes': bboxes_nms,
            'scores': scores_nms,
            'labels': labels_nms
        }
        info_list.append(info_)

    if save_json_file is not None:
        logger.info(f"\n[INFO]: Saving JSON file to: {save_json_file}", **logger_params)
        with open(save_json_file, 'w') as f:
            json.dump(to_serializable(info_list), f)

    if show_results :
        import matplotlib.pyplot as plt

        # Create output directory if it doesn't exist
        output_dir = os.path.join(os.path.dirname(save_json_file), 'visualizations') if save_json_file else 'output_visualizations'
        os.makedirs(output_dir, exist_ok=True)

        # COCO class names for visualization

        for info in info_list:
            logger.info(f"\n[INFO]: Processing image: {info['image_path']}", **logger_params)
            if len(info['bboxes']) == 0:
                continue

            # Load image
            image_path = info['image_path']
            img = cv2.imread(image_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # Draw bounding boxes
            for bbox, score, label in zip(info['bboxes'], info['scores'], info['labels']):
                x1, y1, x2, y2 = map(int, bbox)
                # Draw rectangle
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # Get label text
                label_text = f"{YOLO_LABELS.get(label, f'class_{label}')} {score:.2f}"

                # Put text
                cv2.putText(img, label_text, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # Save the image
            output_path = os.path.join(output_dir, os.path.basename(image_path))

            cv2.imwrite(output_path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
            logger.info(f"Saved visualization to {output_path}", **logger_params)

    return info_list

def run_2d_inference(input_folder, output_folder, config=None):
    """
    Run 2D inference on a folder of images.

    Args:
        input_folder (str): Path to the input folder of images.
        output_folder (str): Path to the output folder.
        config (dict): Configuration dictionary for 2D detection settings.

    Returns:
        None
    """
    config = config or {}
    
    # Collect all image files in the specified folder
    image_paths = glob.glob(os.path.join(input_folder, '*.png'))

    output_path = os.path.join(output_folder, 'detections_2d.json')

    # Detect objects in each image using the 2D model and save output to output path
    detect_objects_with_sahi(image_paths,
                           config=config,
                           save_json_file=output_path)

if __name__ == "__main__":
    # Example config for standalone usage
    example_config = {
        "model_type": "yolov8",
        "model_path": "yolov8x.pt",
        "device": "cuda",
        "base_confidence_threshold": 0.1,
        "filter_class_ids": [0, 1, 2, 3, 5, 7],
        "show_results": True,
        "camera_specific_settings": {
            "FC1": {
                "confidence_threshold": 0.4,
                "slice_height": 512,
                "slice_width": 512
            }
        }
    }
    
    run_2d_inference("/home/<USER>/data/bosch_rfi_sample_data/ext_benchmark_batch_3658182",
                     '/home/<USER>/CV/ml-b_rfi/outputs/2d_detection_output',
                     example_config)
