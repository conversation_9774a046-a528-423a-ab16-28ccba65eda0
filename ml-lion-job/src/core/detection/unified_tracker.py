import numpy as np
from collections import defaultdict
from .track_3d import AB3DMOT
from .track_2d import SiameseTracker
from .association import MultiModalAssociator
from .unified_track import UnifiedTrack
from .id_manager import GlobalIDManager

class UnifiedMultiModalTracker:
    """
    Unified tracker that handles 3D LiDAR and 2D camera data simultaneously
    with consistent ID management across all modalities.
    """
    
    def __init__(self, config):
        self.config = config
        self.id_manager = GlobalIDManager()
        self.tracks = {}  # Global track registry
        self.frame_count = 0
        
        # Initialize modality-specific trackers
        self.lidar_tracker = AB3DMOT("unified", config.get('3d_tracking_config', {}))
        self.camera_trackers = {}
        for cam in config.get('camera_sensors', []):
            if cam != 'RefLidar':  # Exclude RefLidar
                self.camera_trackers[cam] = SiameseTracker(**config.get('2d_tracking_config', {}))
        
        # Initialize associator
        self.associator = MultiModalAssociator(config.get('association_config', {}))
        
    def update(self, lidar_detections, camera_detections, projector):
        """
        Unified update that processes all modalities together
        """
        self.frame_count += 1
        
        # Step 1: Update 3D tracks
        lidar_tracks = self.lidar_tracker.update(lidar_detections)
        
        # Step 2: Project 3D tracks to 2D for association
        projected_tracks = self._project_3d_tracks(lidar_tracks, projector)
        
        # Step 3: Update 2D trackers with their detections
        camera_tracks = {}
        for camera_name, detections in camera_detections.items():
            if camera_name in self.camera_trackers:
                camera_tracks[camera_name] = self.camera_trackers[camera_name].update(detections)
        
        # Step 4: Associate 2D detections with projected 3D tracks
        associations = self.associator.associate(
            projected_tracks, camera_tracks, camera_detections
        )
        
        # Step 5: Update unified tracks
        unified_tracks = self._update_unified_tracks(
            lidar_tracks, camera_tracks, associations
        )
        
        return unified_tracks
    
    def _project_3d_tracks(self, lidar_tracks, projector):
        """Project 3D tracks to all cameras"""
        projected_tracks = {}
        
        for camera_name in self.camera_trackers.keys():
            projected_tracks[camera_name] = []
            
            for track in lidar_tracks:
                if 'cuboid' in track:
                    projected_bbox = projector.project_cuboid(track['cuboid'], camera_name)
                    if projected_bbox is not None:
                        projected_tracks[camera_name].append({
                            '3d_track_id': track.get('track_id'),
                            'projected_bbox': projected_bbox,
                            'confidence': track.get('confidence', 0.5),
                            'class_name': track.get('class_name', 'Unknown')
                        })
        
        return projected_tracks
    
    def _update_unified_tracks(self, lidar_tracks, camera_tracks, associations):
        """Update the unified tracks with new detections"""
        # First, update existing tracks with 3D information
        for track in lidar_tracks:
            track_id = track.get('track_id')
            track_key = f"3d_{track_id}"
            
            global_id = self.id_manager.get_or_create_id(track_key)
            
            if global_id not in self.tracks:
                self.tracks[global_id] = UnifiedTrack(global_id, track, '3d')
            else:
                self.tracks[global_id].update_3d(track, self.frame_count)
        
        # Then update with 2D information and associations
        for camera_name, assocs in associations.items():
            for assoc in assocs:
                if assoc['matched']:
                    # This 2D detection is matched to a 3D track
                    global_id = assoc['global_id']
                    detection_2d = assoc['detection_2d']
                    
                    if global_id in self.tracks:
                        self.tracks[global_id].update_2d(
                            detection_2d, camera_name, self.frame_count
                        )
                else:
                    # This is a 2D-only track
                    track_id = assoc['2d_track_id']
                    track_key = f"2d_{camera_name}_{track_id}"
                    detection_2d = assoc['detection_2d']
                    
                    global_id = self.id_manager.get_or_create_id(track_key)
                    
                    if global_id not in self.tracks:
                        self.tracks[global_id] = UnifiedTrack(global_id, detection_2d, '2d')
                    else:
                        self.tracks[global_id].update_2d(
                            detection_2d, camera_name, self.frame_count
                        )
        
        # Prune old tracks
        self._prune_old_tracks()
        
        return list(self.tracks.values())
    
    def _prune_old_tracks(self):
        """Remove tracks that haven't been seen for a while"""
        max_age = self.config.get('max_track_age', 10)
        
        tracks_to_remove = []
        for track_id, track in self.tracks.items():
            if self.frame_count - track.last_seen > max_age:
                tracks_to_remove.append(track_id)
        
        for track_id in tracks_to_remove:
            del self.tracks[track_id]