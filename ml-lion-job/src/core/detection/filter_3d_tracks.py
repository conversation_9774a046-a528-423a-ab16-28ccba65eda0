import os
import json
import math
from collections import defaultdict
from typing import Dict, List, <PERSON><PERSON>

def calculate_distance(x1: float, y1: float, x2: float, y2: float) -> float:
    """Calculate Euclidean distance between two points."""
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)

def filter_and_reassign_3d_tracks(input_dir: str, output_dir: str, min_frames: int = 3) -> Dict[int, int]:
    """
    Filter 3D tracking results to remove objects detected in less than min_frames
    and reassign continuous IDs to the remaining tracks.
    
    Args:
        input_dir: Directory containing 3D tracking JSON files
        output_dir: Directory to save filtered and reassigned tracking files
        min_frames: Minimum number of frames required for a track to be kept
        
    Returns:
        Dictionary mapping old IDs to new IDs
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Step 1: Count frames for each track ID
    print(f"Step 1: Counting frames per track ID...")
    id_frame_count = defaultdict(set)
    total_tracks = 0
    frame_count = 0
    
    # Get all JSON files in sorted order
    json_files = sorted([f for f in os.listdir(input_dir) if f.endswith('.json')])
    
    for filename in json_files:
        frame_count += 1
        file_path = os.path.join(input_dir, filename)
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Handle both list format and dict format with annotations
            tracks = data if isinstance(data, list) else data.get('annotations', [])
            
            for track in tracks:
                if 'identity' in track:
                    identity = track['identity']
                    id_frame_count[identity].add(filename)
                    total_tracks += 1
                    
        except Exception as e:
            print(f"Error reading {filename}: {e}")
    
    print(f"Found {len(id_frame_count)} unique track IDs across {frame_count} frames")
    print(f"Total track annotations: {total_tracks}")
    
    # Step 2: Filter out IDs that appear in less than min_frames
    print(f"\nStep 2: Filtering out IDs that appear in less than {min_frames} frames...")
    filtered_ids = {id for id, frames in id_frame_count.items() if len(frames) < min_frames}
    kept_ids = {id for id, frames in id_frame_count.items() if len(frames) >= min_frames}
    
    print(f"IDs to be removed: {len(filtered_ids)}")
    print(f"IDs to be kept: {len(kept_ids)}")
    
    # Step 3: Identify tracks in the first frame for ego vehicle detection
    print("\nStep 3: Identifying tracks in first frame for ID assignment...")
    first_frame = json_files[0] if json_files else None
    first_frame_tracks = []
    old_to_new_id_map = {}
    remaining_ids = set(kept_ids)
    
    if first_frame:
        file_path = os.path.join(input_dir, first_frame)
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            tracks = data if isinstance(data, list) else data.get('annotations', [])
            
            # Collect qualified tracks in the first frame
            for track in tracks:
                if ('identity' in track and track['identity'] in kept_ids and
                    'geometry' in track and 'position' in track['geometry']):
                    
                    pos = track['geometry']['position']
                    old_id = track['identity']
                    distance = calculate_distance(0, 0, pos['x'], pos['y'])
                    first_frame_tracks.append((old_id, distance, pos))
                    remaining_ids.discard(old_id)
            
            # Sort by distance from origin (ego vehicle likely closest)
            first_frame_tracks.sort(key=lambda x: x[1])
            
            # Assign continuous IDs starting from 1
            for idx, (old_id, _, _) in enumerate(first_frame_tracks, 1):
                old_to_new_id_map[old_id] = idx
            
            print(f"Assigned continuous IDs 1-{len(first_frame_tracks)} to tracks in first frame")
            
            if first_frame_tracks:
                ego_id, _, ego_pos = first_frame_tracks[0]
                print(f"Identified potential ego vehicle with old ID {ego_id} as new ID 1")
                
        except Exception as e:
            print(f"Error processing first frame {first_frame}: {e}")
    
    # Step 4: Assign IDs to remaining tracks
    print("\nStep 4: Assigning IDs to remaining tracks...")
    next_id = len(first_frame_tracks) + 1
    ego_pos = first_frame_tracks[0][2] if first_frame_tracks else None
    
    # Collect remaining tracks (one instance per ID)
    remaining_tracks = []
    
    for filename in json_files:
        file_path = os.path.join(input_dir, filename)
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            tracks = data if isinstance(data, list) else data.get('annotations', [])
            
            for track in tracks:
                if ('identity' in track and track['identity'] in remaining_ids and
                    'geometry' in track and 'position' in track['geometry']):
                    
                    old_id = track['identity']
                    if old_id not in old_to_new_id_map:
                        pos = track['geometry']['position']
                        remaining_tracks.append((old_id, pos))
                        remaining_ids.discard(old_id)
                        
        except Exception as e:
            print(f"Error reading {filename} for remaining ID assignment: {e}")
    
    # Sort remaining tracks by distance from ego or origin
    sorted_remaining = []
    for old_id, pos in remaining_tracks:
        if ego_pos:
            distance = calculate_distance(ego_pos['x'], ego_pos['y'], pos['x'], pos['y'])
        else:
            distance = calculate_distance(0, 0, pos['x'], pos['y'])
        sorted_remaining.append((old_id, distance))
    
    sorted_remaining.sort(key=lambda x: x[1])
    
    # Assign continuous IDs
    for old_id, _ in sorted_remaining:
        old_to_new_id_map[old_id] = next_id
        next_id += 1
    
    print(f"Assigned IDs {len(first_frame_tracks)+1}-{next_id-1} to {len(sorted_remaining)} remaining tracks")
    
    # Step 5: Process all files with new ID mapping
    print("\nStep 5: Processing all files with new ID mapping...")
    total_filtered = 0
    total_kept = 0
    
    for filename in json_files:
        file_path = os.path.join(input_dir, filename)
        output_file_path = os.path.join(output_dir, filename)
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            tracks = data if isinstance(data, list) else data.get('annotations', [])
            updated_tracks = []
            
            for track in tracks:
                keep_track = True
                
                if 'identity' in track:
                    old_identity = track['identity']
                    if old_identity in filtered_ids:
                        keep_track = False
                        total_filtered += 1
                    else:
                        # Reassign ID
                        if old_identity in old_to_new_id_map:
                            track_copy = json.loads(json.dumps(track))
                            track_copy['identity'] = old_to_new_id_map[old_identity]
                            track = track_copy
                        total_kept += 1
                
                if keep_track:
                    updated_tracks.append(track)
            
            # Save in original format
            if isinstance(data, list):
                output_data = updated_tracks
            else:
                output_data = {'annotations': updated_tracks}
            
            with open(output_file_path, 'w') as f:
                json.dump(output_data, f, indent=4)
                
        except Exception as e:
            print(f"Error processing {filename}: {e}")
    
    # Save ID mapping
    mapping_file = os.path.join(output_dir, 'id_mapping.json')
    with open(mapping_file, 'w') as f:
        json.dump(old_to_new_id_map, f, indent=2)
    
    print(f"\nSummary:")
    print(f"Total frames processed: {frame_count}")
    print(f"Total track annotations: {total_tracks}")
    print(f"Unique track IDs: {len(id_frame_count)}")
    print(f"IDs filtered out: {len(filtered_ids)}")
    print(f"IDs kept: {len(kept_ids)}")
    print(f"Annotations filtered: {total_filtered}")
    print(f"Annotations kept: {total_kept}")
    print(f"ID mapping saved to: {mapping_file}")
    
    return old_to_new_id_map