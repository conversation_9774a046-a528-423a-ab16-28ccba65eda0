import os
import glob
import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms, models
from scipy.optimize import linear_sum_assignment
from collections import defaultdict
import json
from pathlib import Path

# Import logger utilities
from utils import logger
from utils import logger_params

class PretrainedSiameseNetwork(nn.Module):
    """
    Siamese Network using pretrained ResNet backbone for immediate use
    """
    def __init__(self):
        super(PretrainedSiameseNetwork, self).__init__()

        # Use pretrained ResNet18 as backbone
        resnet = models.resnet18(pretrained=True)
        # Remove the final classification layer
        self.backbone = nn.Sequential(*list(resnet.children())[:-1])

        # Feature embedding layer
        self.fc = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128)
        )

        # Freeze backbone weights for stability
        for param in self.backbone.parameters():
            param.requires_grad = False

    def forward_once(self, x):
        """Extract features for one input"""
        x = self.backbone(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return F.normalize(x, p=2, dim=1)  # L2 normalization

    def forward(self, input1, input2):
        """Forward pass for two inputs"""
        output1 = self.forward_once(input1)
        output2 = self.forward_once(input2)
        return output1, output2

class Track:
    """Individual track representation"""
    def __init__(self, track_id, bbox, feature, class_id, confidence, frame_id, frame_id_2d, max_history):
        self.track_id = track_id
        self.bboxes = [bbox]
        self.features = [feature]
        self.class_ids = [class_id]
        self.confidences = [confidence]
        self.frame_ids = [frame_id]
        self.frame_id_2d_list = [frame_id_2d]
        self.last_seen = frame_id
        self.age = 0
        self.hits = 1
        self.time_since_update = 0
        self.max_history = max_history

    def update(self, bbox, feature, class_id, confidence, frame_id, frame_id_2d):
        """Update track with new detection"""
        self.bboxes.append(bbox)
        self.features.append(feature)
        self.class_ids.append(class_id)
        self.confidences.append(confidence)
        self.frame_ids.append(frame_id)
        self.frame_id_2d_list.append(frame_id_2d)
        self.last_seen = frame_id
        self.hits += 1
        self.time_since_update = 0

        # Keep only recent features for efficiency
        if len(self.features) > self.max_history:
            self.features = self.features[-self.max_history:]
            self.bboxes = self.bboxes[-self.max_history:]
            self.class_ids = self.class_ids[-self.max_history:]
            self.confidences = self.confidences[-self.max_history:]
            self.frame_ids = self.frame_ids[-self.max_history:]

    def predict(self):
        """Predict next bounding box using simple motion model"""
        if len(self.bboxes) < 2:
            return self.bboxes[-1]

        # Simple constant velocity model
        current_bbox = np.array(self.bboxes[-1])
        prev_bbox = np.array(self.bboxes[-2])
        velocity = current_bbox - prev_bbox
        predicted_bbox = current_bbox + velocity

        # Ensure bbox stays within reasonable bounds
        predicted_bbox = np.clip(predicted_bbox, 0, 1)
        return predicted_bbox.tolist()

    def get_feature(self):
        """Get most recent feature or average of recent features"""
        if len(self.features) == 0:
            return None
        elif len(self.features) == 1:
            return self.features[-1]
        else:
            # Weight recent features more heavily
            weights = np.exp(np.linspace(0, 1, len(self.features)))
            weights = weights / np.sum(weights)
            weighted_feature = np.average(self.features, axis=0, weights=weights)
            return weighted_feature

class SiameseTracker:
    """
    Siamese-based Multi-Object Tracker
    Uses pretrained features + motion model for robust tracking
    """
    def __init__(self,
                 similarity_threshold=0.5,
                 max_disappeared=30,
                 min_hits=3,
                 motion_weight=0.3,
                 appearance_weight=0.7,
                 config=None):

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}", **logger_params)

        # Initialize pretrained Siamese network
        self.siamese_net = PretrainedSiameseNetwork()
        self.siamese_net.to(self.device)
        self.siamese_net.eval()
        self.config = config

        # Tracker parameters
        self.similarity_threshold = similarity_threshold
        self.max_disappeared = max_disappeared
        self.min_hits = min_hits
        self.motion_weight = motion_weight
        self.appearance_weight = appearance_weight

        # Tracking state
        self.tracks = []
        self.next_id = 1
        self.frame_count = 0

        # Image preprocessing
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),  # ResNet input size
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

    def extract_patch(self, image, bbox):
        """Extract image patch from normalized YOLO bbox"""
        h, w = image.shape[:2]
        x_center, y_center, width, height = bbox

        # Convert to pixel coordinates
        x_center_px = x_center * w
        y_center_px = y_center * h
        width_px = width * w
        height_px = height * h

        # Add padding for better feature extraction
        padding_factor = 0.2
        width_px *= (1 + padding_factor)
        height_px *= (1 + padding_factor)

        # Calculate patch boundaries
        x1 = int(max(0, x_center_px - width_px/2))
        y1 = int(max(0, y_center_px - height_px/2))
        x2 = int(min(w, x_center_px + width_px/2))
        y2 = int(min(h, y_center_px + height_px/2))

        # Extract patch
        if x2 > x1 and y2 > y1:
            patch = image[y1:y2, x1:x2]
            if patch.size > 0:
                return patch

        # Fallback: create small colored patch
        return np.ones((64, 64, 3), dtype=np.uint8) * 128

    def extract_features_batch(self, patches):
        """Extract features for multiple patches efficiently"""
        if len(patches) == 0:
            return []

        # Preprocess all patches
        processed_patches = []
        for patch in patches:
            try:
                processed = self.transform(patch)
                processed_patches.append(processed)
            except Exception as e:
                # Create dummy tensor if preprocessing fails
                processed_patches.append(torch.zeros(3, 224, 224))

        if len(processed_patches) == 0:
            return []

        # Batch process
        batch = torch.stack(processed_patches).to(self.device)

        with torch.no_grad():
            features = self.siamese_net.forward_once(batch)
            return features.cpu().numpy()

    def compute_iou(self, bbox1, bbox2):
        """Compute IoU between two normalized bboxes"""
        def to_corners(bbox):
            x_center, y_center, width, height = bbox
            return [
                x_center - width/2,   # x1
                y_center - height/2,  # y1
                x_center + width/2,   # x2
                y_center + height/2   # y2
            ]

        corners1 = to_corners(bbox1)
        corners2 = to_corners(bbox2)

        # Calculate intersection
        x1 = max(corners1[0], corners2[0])
        y1 = max(corners1[1], corners2[1])
        x2 = min(corners1[2], corners2[2])
        y2 = min(corners1[3], corners2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # Calculate areas
        area1 = (corners1[2] - corners1[0]) * (corners1[3] - corners1[1])
        area2 = (corners2[2] - corners2[0]) * (corners2[3] - corners2[1])

        union = area1 + area2 - intersection
        return intersection / union if union > 0 else 0.0

    def compute_cost_matrix(self, detections, detection_features, img_dims):
        """Compute cost matrix between tracks and detections"""
        if len(self.tracks) == 0 or len(detections) == 0:
            return np.array([]).reshape(0, len(detections)),[]

        cost_matrix = np.full((len(self.tracks), len(detections)), 1e6)

        # Get track features
        track_features = []
        track_predictions = []

        for track in self.tracks:
            feature = track.get_feature()
            prediction = track.predict()

            if feature is not None:
                track_features.append(feature)
                track_predictions.append(prediction)
            else:
                track_features.append(np.zeros(128))
                track_predictions.append(track.bboxes[-1])

        if len(track_features) == 0:
            return cost_matrix

        # Compute appearance similarity
        track_features = np.array(track_features)
        detection_features = np.array(detection_features)

        if detection_features.size > 0:
            # Cosine similarity
            appearance_sim = np.dot(track_features, detection_features.T)
            appearance_sim = np.clip(appearance_sim, -1, 1)
        else:
            appearance_sim = np.zeros((len(self.tracks), len(detections)))

        for i in range(len(track_features)):
            for j in range(len(detection_features)):
                if self.tracks[i].class_ids[-1] != detections[j]['class_id']:
                    appearance_sim[i,j] = 0
        # Compute motion consistency (IoU with prediction)
        motion_sim = np.zeros((len(self.tracks), len(detections)))
        l2_norm = np.zeros((len(self.tracks), len(detections)))
        for i, prediction in enumerate(track_predictions):
            for j, detection in enumerate(detections):
                # Only consider same class
                if self.tracks[i].class_ids[-1] == detection['class_id']:
                    iou = self.compute_iou(prediction, detection['bbox'])
                    img_h, img_w = img_dims
                    pred_x = prediction[0]*img_w
                    pred_y = prediction[1]*img_h
                    det_x = detection['bbox'][0]*img_w
                    det_y = detection['bbox'][1]*img_h
                    l2_norm[i,j] = np.linalg.norm(np.array([pred_x, pred_y]) - np.array([det_x, det_y]))
                    motion_sim[i, j] = iou
                    det_w = detection['bbox'][2]*img_w
                    det_h = detection['bbox'][3]*img_h

                    # for small objects consider l2 norm
                    if iou == 0:
                        if min(det_w, det_h) < self.config.get('min_side', 30) and l2_norm[i,j] < self.config.get('l2_thresh', 100) and detection['class_id'] == 'Pedestrian':
                            motion_sim[i, j] = 1 - min(l2_norm[i,j], 100)/100
                        else:
                            appearance_sim[i,j] = 0

        # Combined similarity
        combined_sim = (self.appearance_weight * appearance_sim +
                       self.motion_weight * motion_sim)

        # Convert to cost (lower is better)
        cost_matrix = 1.0 - combined_sim

        # Set high cost for low similarity
        cost_matrix[combined_sim < self.similarity_threshold] = 1e6
        cost_matrix[l2_norm > 100] = 1e6

        return cost_matrix, track_predictions

    def associate_detections(self, detections, detection_features, image_dims):
        """Associate detections to tracks using Hungarian algorithm"""
        cost_matrix, track_predictions = self.compute_cost_matrix(detections, detection_features, image_dims)

        if cost_matrix.size == 0:
            return [], list(range(len(detections)))

        # Solve assignment problem
        try:
            track_indices, detection_indices = linear_sum_assignment(cost_matrix)

            matches = []
            unmatched_detections = list(range(len(detections)))

            for track_idx, det_idx in zip(track_indices, detection_indices):
                if cost_matrix[track_idx, det_idx] < 1e5:
                    matches.append((track_idx, det_idx))
                    unmatched_detections.remove(det_idx)

            return matches, unmatched_detections

        except Exception as e:
            logger.info(f"Assignment error: {e}", **logger_params)
            return [], list(range(len(detections)))

    def update(self, image, detections):
        """
        Update tracker with new frame
        Args:
            image: RGB image (H, W, 3)
            detections: List of dicts with 'bbox', 'class_id', 'confidence'
        Returns:
            List of active tracks
        """
        self.frame_count += 1

        if len(detections) == 0:
            # No detections - just age existing tracks
            for track in self.tracks:
                track.time_since_update += 1

            # Remove old tracks
            self.tracks = [t for t in self.tracks if t.time_since_update <= self.max_disappeared]
            return self.get_active_tracks()

        # Extract patches and features
        patches = []
        for detection in detections:
            patch = self.extract_patch(image, detection['bbox'])
            patches.append(patch)

        detection_features = self.extract_features_batch(patches)

        if len(detection_features) != len(detections):
            logger.info(f"Warning: Feature extraction failed for some detections", **logger_params)
            return self.get_active_tracks()

        # Associate detections to tracks
        matches, unmatched_detections = self.associate_detections(detections, detection_features, image.shape[:2])

        # Update matched tracks
        for track_idx, det_idx in matches:
            detection = detections[det_idx]
            feature = detection_features[det_idx]

            self.tracks[track_idx].update(
                detection['bbox'],
                feature,
                detection['class_id'],
                detection['confidence'],
                self.frame_count,
                detection['frame_id']
            )

        # Create new tracks for unmatched detections
        for det_idx in unmatched_detections:
            detection = detections[det_idx]
            feature = detection_features[det_idx]

            new_track = Track(
                self.next_id,
                detection['bbox'],
                feature,
                detection['class_id'],
                detection['confidence'],
                self.frame_count,
                detection['frame_id'],
                self.config.get('max_history', 200)
            )
            self.tracks.append(new_track)
            self.next_id += 1

        # Age unmatched tracks
        matched_track_indices = [m[0] for m in matches]
        for i, track in enumerate(self.tracks):
            if i not in matched_track_indices:
                track.time_since_update += 1

        rem_tracks = [i for i in range(len(self.tracks)) if self.tracks[i].time_since_update <= self.max_disappeared]
        # Remove old tracks
        self.tracks = [t for t in self.tracks if t.time_since_update <= self.max_disappeared]

        updated_pos = {}
        for i, t_id in enumerate(rem_tracks):
            updated_pos[t_id] = i

        updated_matches = []
        for i, (t_id, d_id) in enumerate(matches):
            updated_matches.append((updated_pos[t_id], d_id))

        return self.get_active_tracks(), updated_matches

    def get_active_tracks(self):
        """Get currently active tracks"""
        active_tracks = []
        for track in self.tracks:
            # Track is active if it has enough hits and was recently seen
            if track.hits >= self.min_hits and track.time_since_update <= 1:
                active_tracks.append({
                    'track_id': track.track_id,
                    'bbox': track.bboxes[-1],
                    'class_id': track.class_ids[-1],
                    'confidence': track.confidences[-1],
                    'frame_id': track.frame_ids[-1],
                    'hits': track.hits
                })
        return active_tracks

def parse_yolo_annotation(annotation_path):
    """Parse YOLO format annotation file"""
    detections = []
    if os.path.exists(annotation_path):
        with open(annotation_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 6:  # class_id x_center y_center width height confidence
                    detection = {
                        'class_id': int(parts[0]),
                        'bbox': [float(parts[1]), float(parts[2]), float(parts[3]), float(parts[4])],
                        'confidence': float(parts[5])
                    }
                    detections.append(detection)
    return detections

# Class grouping as before
CLASS_MAP = {
    "Vehicle": "Vehicle", "Bus": "Vehicle", "Truck": "Vehicle",
    "Pedestrian": "Pedestrian",
    "Bicycle": "Bike", "Motorbike": "Bike",
}

def convert_to_xywh(bbox, img_dims):
    """
    convert to center_x, center_y, width, height normalized by width and height
    """
    img_h, img_w = img_dims
    x_min, y_min, x_max, y_max = bbox
    return [(x_min + x_max)/(2*img_w), (y_min + y_max)/(2*img_h), (x_max - x_min)/img_w, (y_max - y_min)/img_h]

def get_assigned_id_cams(prev_data, assigned_id):
    cams = []
    for obj in prev_data["objects"]:
        if obj["object_id"] == assigned_id:
            cams.append(obj["2d_projections"].keys())
    return cams


def run_tracking(image_dir,
                 frame_info,
                 camera_name,
                 next_available_id,
                 start_id,
                 config
                 ):
    """
    Run Siamese tracking on image sequence
    """
    # Initialize tracker
    tracker = SiameseTracker(
        similarity_threshold=config.get('similarity_threshold', 0.4),  # Lower threshold for better recall
        max_disappeared=config.get('max_disappeared', 5),        # How long to keep lost tracks
        min_hits=config.get('min_hits', 3),               # Minimum hits before track is confirmed
        motion_weight=config.get('motion_weight', 0.6),        # Weight for motion consistency
        appearance_weight=config.get('appearance_weight', 0.4),     # Weight for appearance similarity
        config=config
    )
    logger.info(f"Tracking {len(frame_info)} frames for {camera_name}...", **logger_params)

    total_detections = 0
    id_mapping = {}

    for frame_id, data in enumerate(frame_info):
        # --- load image ---
        img_path = os.path.join(image_dir, data['frame_info']['file_paths'].get(camera_name, ""))

        if not os.path.exists(img_path) or not img_path.endswith(".png"):
            logger.info(f"⚠️  Image not found: {img_path}", **logger_params)
            continue

        image = cv2.imread(str(img_path))
        if image is None:
            logger.info(f"⚠️  Could not read image: {img_path}", **logger_params)
            continue

        img_h, img_w = image.shape[:2]

        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        raw_dets = []
        detections = []
        for obj_idx, obj in enumerate(data["objects"]):
            for proj in obj["2d_projections"].get(camera_name, []):
                mapped = CLASS_MAP.get(proj.get("label",""), proj.get("label",""))
                x1,y1,x2,y2 = proj["bbox"]
                x1, y1 = int(max(0, x1)), int(max(0, y1))
                x2, y2 = int(min(img_w - 1, x2)), int(min(img_h - 1, y2))
                x_center_norm = (x1 + x2) / (2*img_w)
                y_center_norm = (y1 + y2) / (2*img_h)
                box_w_norm = (x2 - x1)/img_w
                box_h_norm = (y2 - y1)/img_h
                detections.append({
                    "bbox": [x_center_norm, y_center_norm, box_w_norm, box_h_norm],
                    "confidence": proj["confidence"],
                    "class_id": mapped,
                    "frame_id": frame_id
                })
                raw_dets.append({
                    "bbox":       [x1, y1, x2, y2],
                    "confidence": proj["confidence"],
                    "object_id":  obj["object_id"],
                    "proj_iou":   proj["iou"],
                    "class":      mapped,
                    "obj_idx": obj_idx,
                    "has_3d":     bool(obj.get("3d_detection", {}))
                })

        total_detections += len(detections)

        if len(detections) == 0:
            continue

        # Update tracker
        tracks, matches = tracker.update(image, detections)

        # map detections to tracks
        det_to_track = {}
        for t_id, d_id in matches:
            det_to_track[d_id] = t_id

            x_center, y_center, width, height = tracker.tracks[t_id].bboxes[-1]
            x_center_px = x_center * img_w
            y_center_px = y_center * img_h
            width_px = width * img_w
            height_px = height * img_h

            # Calculate patch boundaries
            x1 = int(max(0, x_center_px - width_px/2))
            y1 = int(max(0, y_center_px - height_px/2))
            x2 = int(min(img_w, x_center_px + width_px/2))
            y2 = int(min(img_h, y_center_px + height_px/2))

        for det_idx, d in enumerate(raw_dets):
            if det_idx in det_to_track:
                tr = tracker.tracks[det_to_track[det_idx]]
                tid2d = tr.track_id

                # Determine what ID to assign
                # If object has no 3D detection or projection IoU is 0, use tracker ID
                if d["proj_iou"] == 0:
                    # Check if this tracker ID already has an assigned ID
                    if tid2d not in id_mapping:
                        id_mapping[tid2d] = next_available_id
                        next_available_id += 1

                    # Update the object_id in the JSON data
                    data["objects"][d["obj_idx"]]["object_id"] = id_mapping[tid2d]
                    assigned_id = id_mapping[tid2d]
                else:
                    # Keep original object_id for objects with valid 3D detection
                    assigned_id = d["object_id"]

                    # Also update id_mapping for consistency
                    # new track
                    if tid2d not in id_mapping:
                        id_mapping[tid2d] = assigned_id
                    elif id_mapping[tid2d] != assigned_id:
                        for track_i, frame_2d in enumerate(tr.frame_id_2d_list[:-1]):
                            prev_data = frame_info[frame_2d]
                            prev_ids = [obj["object_id"] for obj in prev_data["objects"]]
                            if assigned_id in prev_ids and camera_name in get_assigned_id_cams(prev_data, assigned_id):
                                continue
                            for obj in prev_data["objects"]:
                                if (obj["object_id"] >= start_id and
                                    obj["object_id"] == id_mapping[tid2d] and
                                    (not obj["3d_detection"]) and
                                    obj["2d_projections"].get(camera_name, [])
                                    and
                                    obj["2d_projections"][camera_name][0]["iou"]  == 0
                                ):
                                    frame_box = convert_to_xywh(obj['2d_projections'][camera_name][0]['bbox'], (img_h, img_w))
                                    track_box = tr.bboxes[track_i]
                                    if tracker.compute_iou(frame_box, track_box) > 0.5:
                                        obj["object_id"] = assigned_id
                                        break
                        id_mapping[tid2d] = assigned_id


    logger.info(f"\n=== Tracking Summary ===", **logger_params)
    logger.info(f"Total frames processed: {len(frame_info)}", **logger_params)
    logger.info(f"Total detections: {total_detections}", **logger_params)

    return next_available_id, frame_info

def get_cur_max_id(json_dir):
    json_files = os.listdir(json_dir)
    max_id = 0
    max_id_json_file = ""
    for json_file in json_files:
        if json_file.endswith(".json"):
            with open(os.path.join(json_dir, json_file), 'r') as f:
                data = json.load(f)
                for obj in data['objects']:
                    if 'object_id' in obj:
                        if obj['object_id'] > max_id:
                            max_id = obj['object_id']
                            max_id_json_file = json_file

    logger.info(f"Found max id: {max_id} in file: {json_dir} {max_id_json_file}", **logger_params)
    logger.info(f"Assigning next id from: {max_id + 100}", **logger_params)
    return max_id + 100


def update_categories(json_file, track_id_to_category):
    for obj in json_file["objects"]:
        obj_id = obj["object_id"]
        class_name = track_id_to_category[obj_id]
        if obj["3d_detection"]:
            obj["3d_detection"]["class"] = class_name
        for cam, projections in obj["2d_projections"].items():
            projections[0]["label"] = class_name

# Example usage
def run_tracking_all_cams(image_dir, json_dir, save_dir, camera_sensors, config):
    # Set your folder paths

    json_files = sorted(
        glob.glob(f"{json_dir}/*.json"),
        key=lambda p: int(Path(p).stem.split("_")[-1]))


    frame_info = []
    for frame_id, json_path in enumerate(json_files):
        with open(json_path,'r') as f:
            data = json.load(open(json_path))
            frame_info.append(data)

    next_available_id = start_id = get_cur_max_id(json_dir)
    for camera_name in camera_sensors:
        next_available_id, frame_info = run_tracking(image_dir, frame_info, camera_name, next_available_id, start_id, config)

    logger.info(f"Processing {len(json_files)} frames...", **logger_params)

    os.makedirs(save_dir, exist_ok=True)

    # Count track length for each track
    track_id_to_frame = defaultdict(list)
    track_id_to_category_cnt = defaultdict(dict)
    for f_id, info in enumerate(frame_info):
        for object in info['objects']:
            track_id_to_frame[object['object_id']].append(f_id)
            if object['2d_projections']:
                for cam, projections in object['2d_projections'].items():
                    if projections[0]['label'] not in track_id_to_category_cnt[object['object_id']]:
                        track_id_to_category_cnt[object['object_id']][projections[0]['label']] = 1
                    else:
                        track_id_to_category_cnt[object['object_id']][projections[0]['label']] += 1
            else:
                if object['3d_detection']['class'] not in track_id_to_category_cnt[object['object_id']]:
                    track_id_to_category_cnt[object['object_id']][object['3d_detection']['class']] = 1
                else:
                    track_id_to_category_cnt[object['object_id']][object['3d_detection']['class']] += 1


    track_id_to_category = {}
    for t_id, category_cnt in track_id_to_category_cnt.items():
        max_cnt = 0
        for category, cnt in category_cnt.items():
            if cnt > max_cnt:
                max_cnt = cnt
                track_id_to_category[t_id] = category

    cnt_tracks = 0
    # remove false positives
    for t_id, frame_list in track_id_to_frame.items():
        if len(frame_list) < config.get('filter_track_len', 3):
            for f_id in frame_list:
                frame_info[f_id]['objects'] = [obj for obj in frame_info[f_id]['objects'] if obj['object_id'] != t_id]
        else:
            cnt_tracks += 1

    # keep vehicle category consistent across frames

    for i,info in enumerate(frame_info):
        update_categories(frame_info[i], track_id_to_category)
        json_out_name = os.path.join(save_dir, os.path.basename(json_files[i]))
        with open(json_out_name, 'w') as f:
            json.dump(frame_info[i], f, indent=2)

    logger.info("Tracking completed! Check tracking_results.json for detailed results.", **logger_params)