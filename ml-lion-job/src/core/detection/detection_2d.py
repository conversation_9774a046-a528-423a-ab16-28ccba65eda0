"""
Module for handling 2D detections from images.
"""

import os
import json
from pathlib import Path

# Import logger utilities
from utils import logger
from utils import logger_params


def get_2d_detections(mapping,detection_json_path):
    """
    Get 2D detections for the mapped image files.
    
    Args:
        mapping (dict): Mapping of stream names to file paths.
        
    Returns:
        dict: Dictionary of 2D detections for each stream.
    """
    try:
        with open(detection_json_path, 'r') as f:
            data = json.load(f)
        
        detections = {}
        for item in mapping:
            if item == 'RefLidar':  # Skip LiDAR files
                continue
                
            for image_data in data:
                if mapping[item] == image_data['image_path']:
                    detections[item] = image_data
                    break
        
        return detections
    except Exception as e:
        logger.info(f"Error retrieving 2D detections: {e}", **logger_params)
        return {}