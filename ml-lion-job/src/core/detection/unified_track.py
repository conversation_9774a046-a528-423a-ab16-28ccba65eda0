import numpy as np
from collections import defaultdict

class UnifiedTrack:
    """
    Unified track that combines 3D and 2D information
    """
    
    def __init__(self, global_id, initial_data, modality):
        self.global_id = global_id
        self.modality = modality  # '3d' or '2d'
        self.frame_count = 0
        self.last_update_frame = 0
        
        # 3D information
        self.cuboid_3d = None
        self.position_3d = None
        self.velocity_3d = None
        self.confidence_3d = 0.0
        
        # 2D information per camera
        self.detections_2d = defaultdict(list)
        self.bboxes_2d = defaultdict(list)
        self.confidences_2d = defaultdict(list)
        
        # Common attributes
        self.class_name = 'Unknown'
        self.track_id_3d = None
        self.track_ids_2d = defaultdict(lambda: None)
        
        # Initialize with first detection
        if modality == '3d':
            self.update_3d(initial_data, 0)
        else:
            self.update_2d(initial_data, 'unknown', 0)
    
    def update_3d(self, detection_3d, frame_num):
        """Update with 3D detection data"""
        self.last_update_frame = frame_num
        self.frame_count += 1
        
        if 'cuboid' in detection_3d:
            self.cuboid_3d = detection_3d['cuboid']
            self.position_3d = detection_3d['cuboid'][:3]  # x, y, z
        
        self.confidence_3d = detection_3d.get('confidence', 0.5)
        self.class_name = detection_3d.get('class_name', self.class_name)
        self.track_id_3d = detection_3d.get('track_id')
        
        if 'velocity' in detection_3d:
            self.velocity_3d = detection_3d['velocity']
    
    def update_2d(self, detection_2d, camera_name, frame_num):
        """Update with 2D detection data"""
        self.last_update_frame = frame_num
        
        if 'bbox' in detection_2d:
            self.bboxes_2d[camera_name].append(detection_2d['bbox'])
            
        confidence = detection_2d.get('confidence', 0.5)
        self.confidences_2d[camera_name].append(confidence)
        
        self.class_name = detection_2d.get('class_name', self.class_name)
        
        if 'track_id' in detection_2d:
            self.track_ids_2d[camera_name] = detection_2d['track_id']
        
        # Keep only recent detections (last 10 frames)
        max_history = 10
        if len(self.bboxes_2d[camera_name]) > max_history:
            self.bboxes_2d[camera_name] = self.bboxes_2d[camera_name][-max_history:]
            self.confidences_2d[camera_name] = self.confidences_2d[camera_name][-max_history:]
    
    def get_latest_2d_detection(self, camera_name):
        """Get the most recent 2D detection for a camera"""
        if camera_name in self.bboxes_2d and self.bboxes_2d[camera_name]:
            return {
                'bbox': self.bboxes_2d[camera_name][-1],
                'confidence': self.confidences_2d[camera_name][-1],
                'class_name': self.class_name
            }
        return None
    
    def has_3d_data(self):
        """Check if track has 3D data"""
        return self.cuboid_3d is not None
    
    def has_2d_data(self, camera_name=None):
        """Check if track has 2D data"""
        if camera_name:
            return camera_name in self.bboxes_2d and len(self.bboxes_2d[camera_name]) > 0
        return len(self.bboxes_2d) > 0
    
    def to_dict(self):
        """Convert track to dictionary for serialization"""
        result = {
            'global_id': self.global_id,
            'class_name': self.class_name,
            'frame_count': self.frame_count,
            'last_update_frame': self.last_update_frame,
            'modality': self.modality
        }
        
        # Add 3D data if available
        if self.has_3d_data():
            result['3d_data'] = {
                'cuboid': self.cuboid_3d.tolist() if isinstance(self.cuboid_3d, np.ndarray) else self.cuboid_3d,
                'position': self.position_3d.tolist() if isinstance(self.position_3d, np.ndarray) else self.position_3d,
                'confidence': self.confidence_3d,
                'track_id': self.track_id_3d
            }
            
            if self.velocity_3d is not None:
                result['3d_data']['velocity'] = self.velocity_3d.tolist() if isinstance(self.velocity_3d, np.ndarray) else self.velocity_3d
        
        # Add 2D data if available
        if self.has_2d_data():
            result['2d_data'] = {}
            for camera_name in self.bboxes_2d:
                if self.bboxes_2d[camera_name]:
                    result['2d_data'][camera_name] = {
                        'latest_bbox': self.bboxes_2d[camera_name][-1],
                        'latest_confidence': self.confidences_2d[camera_name][-1],
                        'track_id': self.track_ids_2d[camera_name],
                        'detection_count': len(self.bboxes_2d[camera_name])
                    }
        
        return result
