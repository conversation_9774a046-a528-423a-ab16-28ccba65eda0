import numpy as np
from scipy.optimize import linear_sum_assignment
from scipy.spatial.distance import cdist
import cv2

class MultiModalAssociator:
    """
    Associates 2D detections with projected 3D tracks using appearance and IoU
    """
    
    def __init__(self, config):
        self.config = config
        self.appearance_weight = config.get('appearance_weight', 0.4)
        self.iou_weight = config.get('iou_weight', 0.6)
        self.iou_threshold = config.get('iou_threshold', 0.1)
        self.similarity_threshold = config.get('similarity_threshold', 0.5)
    
    def associate(self, projected_tracks, camera_tracks, camera_detections):
        """
        Associate 2D detections with projected 3D tracks
        
        Args:
            projected_tracks: Dict of camera_name -> list of projected 3D tracks
            camera_tracks: Dict of camera_name -> list of 2D tracks
            camera_detections: Dict of camera_name -> list of 2D detections
            
        Returns:
            Dict of associations per camera
        """
        associations = {}
        
        for camera_name in projected_tracks.keys():
            proj_tracks = projected_tracks.get(camera_name, [])
            cam_tracks = camera_tracks.get(camera_name, [])
            cam_detections = camera_detections.get(camera_name, [])
            
            associations[camera_name] = self._associate_camera(
                proj_tracks, cam_tracks, cam_detections
            )
        
        return associations
    
    def _associate_camera(self, projected_tracks, camera_tracks, detections):
        """Associate detections for a single camera"""
        if not projected_tracks or not detections:
            # Return unmatched detections
            return [{'matched': False, 'detection_2d': det, '2d_track_id': None} 
                   for det in detections]
        
        # Compute cost matrix
        cost_matrix = self._compute_cost_matrix(projected_tracks, detections)
        
        # Solve assignment problem
        if cost_matrix.size > 0:
            row_indices, col_indices = linear_sum_assignment(cost_matrix)
            
            # Filter out assignments with high cost
            valid_assignments = []
            for row, col in zip(row_indices, col_indices):
                if cost_matrix[row, col] < (1 - self.similarity_threshold):
                    valid_assignments.append((row, col))
        else:
            valid_assignments = []
        
        # Create association results
        associations = []
        matched_detections = set()
        
        # Add matched associations
        for proj_idx, det_idx in valid_assignments:
            proj_track = projected_tracks[proj_idx]
            detection = detections[det_idx]
            
            associations.append({
                'matched': True,
                'global_id': proj_track.get('3d_track_id'),
                'detection_2d': detection,
                '2d_track_id': None
            })
            matched_detections.add(det_idx)
        
        # Add unmatched detections
        for i, detection in enumerate(detections):
            if i not in matched_detections:
                associations.append({
                    'matched': False,
                    'detection_2d': detection,
                    '2d_track_id': i  # Temporary ID
                })
        
        return associations
    
    def _compute_cost_matrix(self, projected_tracks, detections):
        """Compute cost matrix between projected tracks and detections"""
        if not projected_tracks or not detections:
            return np.array([])
        
        n_tracks = len(projected_tracks)
        n_detections = len(detections)
        cost_matrix = np.ones((n_tracks, n_detections))
        
        for i, proj_track in enumerate(projected_tracks):
            for j, detection in enumerate(detections):
                # IoU cost
                iou_cost = 1 - self._compute_iou(
                    proj_track['projected_bbox'], 
                    detection.get('bbox', [])
                )
                
                # Appearance cost (simplified - using class similarity)
                appearance_cost = self._compute_appearance_cost(
                    proj_track, detection
                )
                
                # Combined cost
                cost_matrix[i, j] = (
                    self.iou_weight * iou_cost + 
                    self.appearance_weight * appearance_cost
                )
        
        return cost_matrix
    
    def _compute_iou(self, bbox1, bbox2):
        """Compute IoU between two bounding boxes"""
        if not bbox1 or not bbox2 or len(bbox1) < 4 or len(bbox2) < 4:
            return 0.0
        
        # Convert to [x1, y1, x2, y2] format
        x1_1, y1_1, x2_1, y2_1 = bbox1[:4]
        x1_2, y1_2, x2_2, y2_2 = bbox2[:4]
        
        # Compute intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # Compute union
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _compute_appearance_cost(self, proj_track, detection):
        """Compute appearance similarity cost"""
        # Simple class-based similarity
        proj_class = proj_track.get('class_name', 'Unknown')
        det_class = detection.get('class_name', 'Unknown')
        
        if proj_class == det_class:
            return 0.0  # Same class, low cost
        else:
            return 1.0  # Different class, high cost