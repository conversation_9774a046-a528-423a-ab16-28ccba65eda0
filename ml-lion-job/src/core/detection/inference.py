from .predict_openpcdet import run_3d_inference
from .track_3d import run_tracker
from .detect_2d import run_2d_inference
from .improved_pipeline import run_improved_tracking_pipeline
import os
from utils import logger
from utils import logger_params


def run_3d_2d_inference(data_path, 
                 extn, 
                 model_path, 
                 cfg_path, 
                 detection_3d_output_folder, 
                 tracking_3d_output_folder,
                 detection_2d_output_folder,
                 config=None):
    """
    Run 3D and 2D inference with configurable parameters.
    
    Args:
        data_path: Path to the data directory
        extn: File extension for point cloud files
        model_path: Path to the 3D detection model
        cfg_path: Path to the model configuration
        detection_3d_output_folder: Output folder for 3D detections
        tracking_3d_output_folder: Output folder for 3D tracking
        detection_2d_output_folder: Output folder for 2D detections
        config: Configuration dictionary
    
    Returns:
        Tuple of output paths
    """
    # Check if we should use the improved pipeline
    use_improved_pipeline = config.get('use_improved_pipeline', False)
    
    if use_improved_pipeline:
        logger.info("Using improved unified tracking pipeline...", **logger_params)
        
        # First run the individual detections
        logger.info("Running 3D detection...", **logger_params)
        run_3d_inference(data_path, extn, model_path, cfg_path, detection_3d_output_folder, config.get('3d_detection_config', {}))
        
        logger.info("Running 2D detection...", **logger_params)
        run_2d_inference(data_path, detection_2d_output_folder, config.get('2d_detection_config', {}))
        
        # Then run the improved pipeline
        unified_tracking_output = os.path.join(os.path.dirname(tracking_3d_output_folder), 'unified_tracking')
        
        # Create a combined config for the unified pipeline
        unified_config = {
            'camera_sensors': config.get('camera_sensors', []),
            '3d_tracking_config': config.get('3d_tracking_config', {}),
            '2d_tracking_config': config.get('2d_tracking_config', {}),
            'association_config': {
                'appearance_weight': 0.4,
                'iou_weight': 0.6,
                'iou_threshold': 0.1,
                'similarity_threshold': 0.5
            },
            'max_track_age': 10
        }
        
        run_improved_tracking_pipeline(data_path, unified_tracking_output, unified_config)
        
        # For backward compatibility, also create the traditional outputs
        logger.info("Creating backward-compatible outputs...", **logger_params)
        convert_unified_to_traditional(unified_tracking_output, tracking_3d_output_folder)
        
        return detection_3d_output_folder, tracking_3d_output_folder, detection_2d_output_folder
    else:
        # Original pipeline
        logger.info("Running 3D detection...", **logger_params)
        run_3d_inference(data_path, extn, model_path, cfg_path, detection_3d_output_folder, config.get('3d_detection_config', {}))
        
        logger.info("Running 3D tracking...", **logger_params)
        run_tracker(detection_3d_output_folder, tracking_3d_output_folder)
        
        logger.info("Running 2D detection...", **logger_params)
        run_2d_inference(data_path, detection_2d_output_folder, config.get('2d_detection_config', {}))
