class GlobalIDManager:
    """
    Manages global IDs across all modalities to ensure consistency
    """
    
    def __init__(self):
        self.track_key_to_id = {}  # Maps track keys to global IDs
        self.next_id = 1
        self.id_to_track_key = {}  # Reverse mapping
    
    def get_or_create_id(self, track_key):
        """
        Get existing global ID for track key or create new one
        
        Args:
            track_key: Unique identifier for the track (e.g., "3d_123", "2d_FC1_456")
            
        Returns:
            Global ID (integer)
        """
        if track_key in self.track_key_to_id:
            return self.track_key_to_id[track_key]
        
        # Create new global ID
        global_id = self.next_id
        self.next_id += 1
        
        self.track_key_to_id[track_key] = global_id
        self.id_to_track_key[global_id] = track_key
        
        return global_id
    
    def get_track_key(self, global_id):
        """Get track key for a global ID"""
        return self.id_to_track_key.get(global_id)
    
    def remove_id(self, global_id):
        """Remove a global ID (when track is deleted)"""
        if global_id in self.id_to_track_key:
            track_key = self.id_to_track_key[global_id]
            del self.id_to_track_key[global_id]
            del self.track_key_to_id[track_key]
    
    def get_stats(self):
        """Get statistics about ID usage"""
        return {
            'total_ids_created': self.next_id - 1,
            'active_ids': len(self.track_key_to_id),
            'next_available_id': self.next_id
        }
