"""
Module for handling 3D detections from LiDAR data.
"""

import os
import json
from pathlib import Path

# Import logger utilities
from utils import logger
from utils import logger_params


def get_3d_detections(lidar_file, tracker_json_path):
    """
    Get 3D detections from LiDAR tracking output.
    
    Args:
        lidar_file (str): Path to the LiDAR file.
        
    Returns:
        list: List of 3D detections.
    """
    lidar_file = Path(lidar_file)

    # Get corresponding tracker JSON file
    tracker_json = os.path.join(f'{tracker_json_path}', 'tracked_object_labels_' + lidar_file.stem + '.json')
    if not os.path.exists(tracker_json):
        logger.info(f"Tracker JSON file does not exist: {tracker_json}", **logger_params)
        return []   
      
    try:
        with open(tracker_json, 'r') as f:
            tracker_data = json.load(f)
    except Exception as e:
        logger.info(f"Error reading tracker JSON: {e}", **logger_params)
        return []
    
    # Process the data into a standard format
    detections = []
    for obj in tracker_data:
        pos = obj["geometry"]["position"]
        cx, cy, cz = float(pos["x"]), float(pos["y"]), float(pos["z"])

        size = obj["geometry"]["boxSize"]
        dx, dy, dz = float(size["x"]), float(size["y"]), float(size["z"])

        yaw = float(obj["geometry"]["rotation"]["z"])
        
        obj_id = obj.get('identity', None)
        class_name = obj.get('class', 'unknown')
        
        detections.append({
            'id': obj_id,
            'class': class_name,
            'cuboid': [cx, cy, cz, dx, dy, dz, yaw]
        })
    
    return detections