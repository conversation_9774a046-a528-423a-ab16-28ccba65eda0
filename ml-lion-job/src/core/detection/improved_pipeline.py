import os
import json
import numpy as np
from tqdm import tqdm
from .unified_tracker import UnifiedMultiModalTracker
from .id_manager import GlobalIDManager
from ..projection import CuboidProjector
from ..calibration import get_calibration
from utils import logger
from utils import logger_params

def run_improved_tracking_pipeline(seq_dir, output_dir, config):
    """
    Improved pipeline that processes 3D and 2D data together
    """
    logger.info("Starting improved tracking pipeline...", **logger_params)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get calibration data - need to find the calibration file first
    logger.info("Loading calibration data...", **logger_params)
    
    # Find calibration JSON file in the sequence directory
    json_files = [f for f in os.listdir(seq_dir) if f.endswith('.json')]
    if not json_files:
        raise ValueError(f"No JSON calibration files found in {seq_dir}")
    
    # Use the first JSON file as calibration file
    calib_file = os.path.join(seq_dir, json_files[0])
    camera_sensors = config.get('camera_sensors', ['FC1', 'TV_right', 'TV_left', 'TV_front', 'TV_rear', 'RefLidar'])
    
    calibration_data = get_calibration(calib_file, 'RefLidar', camera_sensors)
    
    # Initialize projector
    projector = CuboidProjector(calibration_data)
    
    # Initialize unified tracker
    unified_tracker = UnifiedMultiModalTracker(config)
    
    # Get frame sequence from the 3D detection output folder
    detection_3d_folder = os.path.join(os.path.dirname(output_dir), '3d_detection_output')
    if not os.path.exists(detection_3d_folder):
        # Fallback to looking in seq_dir/3d_detections
        detection_3d_folder = os.path.join(seq_dir, '3d_detections')
        if not os.path.exists(detection_3d_folder):
            raise ValueError(f"3D detection folder not found. Looked in: {detection_3d_folder}")
    
    frame_files = sorted([f for f in os.listdir(detection_3d_folder) 
                         if f.endswith('.json')])
    
    # Process each frame
    logger.info(f"Processing {len(frame_files)} frames...", **logger_params)
    
    all_results = []
    
    for frame_idx, frame_file in enumerate(tqdm(frame_files)):
        # Use frame_idx as frame_id since filenames are complex
        frame_id = frame_idx
        
        # Get 3D detections
        lidar_detections = load_3d_detections(os.path.join(detection_3d_folder, frame_file))
        
        # Get 2D detections for each camera
        camera_detections = {}
        detection_2d_folder = os.path.join(os.path.dirname(output_dir), '2d_detection_output')
        for camera in config.get('camera_sensors', []):
            if camera != 'RefLidar':  # Skip RefLidar
                # Try different naming patterns for 2D detection files
                possible_paths = [
                    os.path.join(detection_2d_folder, camera, f"{frame_id:06d}.json"),
                    os.path.join(detection_2d_folder, camera, f"{frame_idx:06d}.json"),
                    os.path.join(detection_2d_folder, camera, frame_file),
                    os.path.join(detection_2d_folder, camera, frame_file.replace('.json', f'_{camera}.json'))
                ]
                
                camera_detections[camera] = []
                for camera_det_path in possible_paths:
                    if os.path.exists(camera_det_path):
                        camera_detections[camera] = load_2d_detections(camera_det_path)
                        break
        
        # Run unified tracking
        unified_tracks = unified_tracker.update(lidar_detections, camera_detections, projector)
        
        # Create frame output
        frame_output = {
            'frame_id': frame_id,
            'tracks': [track.to_dict() for track in unified_tracks]
        }
        
        all_results.append(frame_output)
        
        # Save frame results - use frame_idx for consistent naming
        output_path = os.path.join(output_dir, f"{frame_idx:06d}.json")
        with open(output_path, 'w') as f:
            json.dump(frame_output, f, default=to_serializable)
    
    # Save summary results
    summary_path = os.path.join(output_dir, "tracking_summary.json")
    with open(summary_path, 'w') as f:
        json.dump({
            'total_frames': len(frame_files),
            'results': all_results
        }, f, default=to_serializable)
    
    logger.info(f"Improved tracking pipeline completed. Results saved to {output_dir}", **logger_params)
    return output_dir

def load_3d_detections(file_path):
    """Load 3D detections from JSON file and convert to format expected by tracker"""
    if not os.path.exists(file_path):
        return []
        
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    detections = []
    
    # Handle different formats
    if isinstance(data, list):
        # Check if it's the prediction format from 3D detection
        if len(data) > 0 and 'pred_boxes' in data[0]:
            # This is the format from 3D detection output
            pred_boxes = np.array(data[0].get("pred_boxes", []))
            pred_scores = np.array(data[0].get("pred_scores", []))
            pred_labels = np.array(data[0].get("pred_labels", []))
            
            for i, box in enumerate(pred_boxes):
                # Convert from [x,y,z,l,w,h,yaw] to [x,y,z,yaw,l,w,h] format expected by tracker
                if len(box) >= 7:
                    detection = np.array([box[0], box[1], box[2], box[6], box[3], box[4], box[5]])
                    detections.append(detection)
        else:
            # Handle other list formats
            for ann in data:
                if isinstance(ann, dict):
                    detection = convert_dict_to_detection_array(ann)
                    if detection is not None:
                        detections.append(detection)
    else:
        # Handle dictionary format
        annotations = data.get('annotations', [])
        for ann in annotations:
            detection = convert_dict_to_detection_array(ann)
            if detection is not None:
                detections.append(detection)
    
    return np.array(detections) if detections else np.empty((0, 7))

def convert_dict_to_detection_array(ann):
    """Convert dictionary annotation to detection array format [x,y,z,yaw,l,w,h]"""
    try:
        # Try different possible field names
        x = ann.get('center_x', ann.get('x', 0))
        y = ann.get('center_y', ann.get('y', 0))
        z = ann.get('center_z', ann.get('z', 0))
        
        l = ann.get('length', ann.get('l', 0))
        w = ann.get('width', ann.get('w', 0))
        h = ann.get('height', ann.get('h', 0))
        
        yaw = ann.get('rotation_z', ann.get('yaw', ann.get('rotation', 0)))
        
        # Check if we have valid dimensions
        if l > 0 and w > 0 and h > 0:
            return np.array([x, y, z, yaw, l, w, h])
        else:
            return None
    except (KeyError, TypeError):
        return None

def load_2d_detections(file_path):
    """Load 2D detections from JSON file"""
    if not os.path.exists(file_path):
        return []
        
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    detections = []
    
    # Handle different formats
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                # Extract bounding box and other info
                bbox = item.get('bbox', [])
                if len(bbox) >= 4:
                    detection = {
                        'bbox': bbox,  # [x1, y1, x2, y2]
                        'confidence': item.get('confidence', item.get('score', 0.5)),
                        'class_name': item.get('class_name', item.get('label', 'Unknown')),
                        'class_id': item.get('class_id', 0)
                    }
                    detections.append(detection)
    
    return detections

def to_serializable(obj):
    """Convert numpy arrays and other non-serializable objects to serializable format"""
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif hasattr(obj, '__dict__'):
        return obj.__dict__
    else:
        return str(obj)
