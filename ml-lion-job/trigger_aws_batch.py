import boto3
import pandas as pd

# Initialize the Batch client
batch_client = boto3.client('batch', region_name='us-east-1')

# Job parameters

job_queue = "ml-lion-job-jq"
job_definition = "ml-lion-job-jd"

# Load the CSV file
csv_file_path = "batch.csv"
df = pd.read_csv(csv_file_path)


# Submit the job
for index, row in df.iterrows():
    print(row['s3_prefix'])
    job_name = f"ml-lion-job__{row['s3_prefix'].split('/')[-1]}"
    response = batch_client.submit_job(
        jobName=job_name,
        jobQueue=job_queue,
        jobDefinition=job_definition,
        containerOverrides={
            "environment": [
                {"name": "S3_PREFIX", "value": row['s3_prefix']},
                {"name": "ENVIRONMENT", "value": "playground"},
                {"name": "SEQUENCE_COUNT", "value": str(row['seq_count'])},
                {"name": "OUTPUT_SUFFIX", "value": row['output_suffix']},
            ],
            "resourceRequirements": [
                {"type": "VCPU", "value": "4"},
                {"type": "MEMORY", "value": "8192"},
                {"type": "GPU", "value": "1"},
            ]
        },
        retryStrategy={
            "attempts": 1
        },
        timeout={
            "attemptDurationSeconds": 3600
        }
    )
    print("Submitted job:")
    print(f"Job ID: {response['jobId']}, Job Name: {response['jobName']}")
